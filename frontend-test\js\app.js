/**
 * @文件概述: Vue应用主入口文件 - 重构后的核心应用逻辑
 * @重构时间: 2025-06-12
 * @重构目标: 将原来2223行的单文件拆分为模块化架构
 * @职责范围:
 *   - Vue应用初始化和配置
 *   - 核心状态管理
 *   - 文件上传功能
 *   - 导航控制

 */

// ============================================================================
// 1. Vue 3 和 Element Plus 导入
// ============================================================================

const { createApp, ref, computed, watch, onMounted, onBeforeUnmount, onUnmounted } = Vue;
const { ElContainer, ElHeader, ElMain, ElButton, ElMessage, ElButtonGroup, ElLoading, ElProgress, ElSelect, ElOption, ElSlider, ElIcon, ElSwitch, ElInput, ElDialog, ElCheckbox, ElTag } = ElementPlus;

// ============================================================================
// 2. 模式管理工具函数
// ============================================================================

/**
 * @功能概述: 视频重复模式管理工具
 */
const RepeatModeManager = {
    // 可用模式定义
    AVAILABLE_MODES: {
        blindListen: "盲听",
        clozedSubtitle: "单词填空", 
        bilingualSubtitle: "中英翻译"
    },

    // 中文数字映射
    CHINESE_NUMBERS: ["一", "二", "三", "四", "五"],

    /**
     * 根据模式名称获取显示文本
     */
    getModeDisplayText(modeName) {
        return this.AVAILABLE_MODES[modeName] || "未知模式";
    },

    /**
     * 根据索引和模式名称生成完整的显示文本
     */
    generateDisplayText(index, modeName) {
        const chineseNumber = this.CHINESE_NUMBERS[index] || (index + 1);
        const modeText = this.getModeDisplayText(modeName);
        return `第${chineseNumber}遍 ${modeText}`;
    },

    /**
     * 获取所有可用模式的选项列表
     */
    getAvailableModeOptions() {
        return Object.entries(this.AVAILABLE_MODES).map(([name, displayText]) => ({
            name,
            displayText
        }));
    },

    /**
     * 创建新的模式元素（默认为盲听）
     */
    createNewMode(index) {
        return {
            name: "blindListen",
            displayText: this.generateDisplayText(index, "blindListen")
        };
    },

    /**
     * 获取默认的模式序列（经典模式）
     */
    getDefaultModeSequence(count) {
        const defaultSequence = ["blindListen", "clozedSubtitle", "bilingualSubtitle"];
        const result = [];
        
        for (let i = 0; i < count; i++) {
            const modeName = defaultSequence[i % defaultSequence.length];
            result.push({
                name: modeName,
                displayText: this.generateDisplayText(i, modeName)
            });
        }
        
        return result;
    },

    /**
     * 重新生成所有模式的显示文本
     */
    regenerateDisplayTexts(repeatModes) {
        return repeatModes.map((mode, index) => ({
            ...mode,
            displayText: this.generateDisplayText(index, mode.name)
        }));
    }
};

// ============================================================================
// 3. 核心状态管理模块
// ============================================================================

/**
 * @功能概述: 创建应用核心状态
 * @返回值: {object} 包含所有响应式状态的对象
 */
function createAppState() {
    // 界面状态
    const activeSection = ref('upload'); // 当前激活的功能区域
    const isUploading = ref(false); // 上传状态标记
    const selectedFile = ref(null); // 用户选择的文件
    const isExitingEdit = ref(false); // 🆕 退出编辑中状态

    // 项目选择相关状态
    const projectList = ref([]); // 项目列表数据
    const selectedProject = ref(null); // 当前选中的项目
    const isLoadingProjects = ref(false); // 项目列表加载状态
    const projectLoadError = ref(null); // 项目加载错误信息
    const projectPagination = ref({ // 分页信息
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
    });

    // 下载链接状态
    const downloadLinks = ref({
        enhancedBilingualSubtitle: null,  // 增强双语字幕文件下载链接
        finalVideo: null                  // 最终视频文件下载链接
    });

    // 项目选择对话框状态
    const showProjectSelectionDialog = ref(false); // 项目选择对话框显示状态
    const dialogSelectedProject = ref(null); // 对话框中选中的项目

    // 生成视频查看对话框状态
    const showGeneratedVideosDialog = ref(false); // 对话框显示状态
    const generatedVideosList = ref([]); // 生成的视频列表
    const selectedVideos = ref(new Set()); // 选中的视频集合
    const selectAllVideos = ref(false); // 全选状态
    const currentProjectId = ref(null); // 当前查看的项目ID
    const loadingGeneratedVideos = ref(false); // 加载状态
    const videoStatusMap = ref(new Map()); // 视频发布状态映射
    const markingAsPublished = ref(false); // 标注发布状态的loading状态
    const batchDownloading = ref(false); // 批量下载的loading状态
    const generatingLiveVideoDraft = ref(false); // 生成直播视频草稿的loading状态

    const editorData = ref({
        // 校正后的完整文本内容
        correctedFullText: null,
        // 视频播放URL
        videoPlaybackUrl: null,
        // 视频标识符
        videoIdentifier: null,
        // 原始视频文件路径
        originalVideoPath: null,
        // 原始视频文件名
        originalVideoName: null,
        // 英文字幕内容（SRT格式）
        englishSrtContent: null,
        // 中文字幕内容（SRT格式）
        chineseSrtContent: null,
        // 视频编辑状态
    });

    // 视频编辑状态
//     const editorData = ref({
//         // 校正后的完整文本内容
//         correctedFullText: 'Good afternoon, there is another wildfire evacuation alert tonight. The County of Northern Lights is telling residents of Hawk Hills, which is near Twin Lakes Provincial Park to be ready to evacuate at any time. Firefighters across northern Alberta have been trying to contain several out-of-control wildfires, and it has been an uphill battle with one community losing dozens of structures. Residents in the Inglewood neighborhood say they were woken up to a scene that felt straight out of a movie, a potentially impaired driver wreaking havoc, damaging multiple vehicles. Jasmine King explains. The homicide unit has been called in to investigate after a man was found dead in a downtown construction has hit another key route in the core. 107th Avenue has lane closures heading east and west. As Lisa McGregor reports, there are now roadblocks on every main route downtown in 48 hours. The Oilers will host Game 1 of the Stanley Cup Final, looking for some revenge against the Florida Panthers, who beat the team in seven last season. Our Slap Cornick joins me now in studio. Canada\'s premieres pitched projects to the Prime Minister today, including Danielle Smith, who\'s pushing for an oil pipeline. Morgan Black reports.',
//         // 视频播放URL
//         videoPlaybackUrl: 'http://localhost:8081/backend/uploads/videoFile-1749090956973-678162462.mp4',
//         // 视频标识符
//         videoIdentifier: 'videoFile-1749090956973-678162462',
//         // 原始视频文件路径
//         originalVideoPath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\videoFile-1749090956973-678162462.mp4',
//         // 原始视频文件名
//         originalVideoName: 'videoFile-1749090956973-678162462.mp4',
//         // 英文字幕内容（SRT格式）
//         englishSrtContent: `1
// 00:00:00,000 --> 00:00:04,000
// Good afternoon, there is another wildfire evacuation alert tonight. The County of

// 2
// 00:00:04,000 --> 00:00:08,199
// Northern Lights is telling residents of Hawk Hills, which is near Twin Lakes

// 3
// 00:00:08,199 --> 00:00:12,760
// Provincial Park to be ready to evacuate at any time. Firefighters across

// 4
// 00:00:12,760 --> 00:00:16,079
// northern Alberta have been trying to contain several out-of-control

// 5
// 00:00:16,079 --> 00:00:20,280
// wildfires, and it has been an uphill battle with one community losing

// 6
// 00:00:20,280 --> 00:00:24,920
// dozens of structures. Residents in the Inglewood neighborhood say they

// 7
// 00:00:24,920 --> 00:00:29,399
// were woken up to a scene that felt straight out of a movie, a potentially

// 8
// 00:00:29,399 --> 00:00:33,900
// impaired driver wreaking havoc, damaging multiple vehicles. Jasmine

// 9
// 00:00:33,900 --> 00:00:38,599
// King explains. The homicide unit has been called in to investigate after a

// 10
// 00:00:38,599 --> 00:00:43,799
// man was found dead in a downtown construction has hit another key route

// 11
// 00:00:43,799 --> 00:00:49,099
// in the core. 107th Avenue has lane closures heading east and west. As Lisa

// 12
// 00:00:49,099 --> 00:00:53,479
// McGregor reports, there are now roadblocks on every main route downtown

// 13
// 00:00:53,500 --> 00:00:58,080
// in 48 hours. The Oilers will host Game 1 of the Stanley Cup Final, looking

// 14
// 00:00:58,080 --> 00:01:02,919
// for some revenge against the Florida Panthers, who beat the team in seven last

// 15
// 00:01:02,919 --> 00:01:07,360
// season. Our Slap Cornick joins me now in studio. Canada's premieres pitched

// 16
// 00:01:07,360 --> 00:01:14,279
// projects to the Prime Minister today, including Danielle Smith, who's pushing for an oil pipeline. Morgan Black reports.`,
//         // 中文字幕内容（SRT格式）
//         chineseSrtContent: `1
// 00:00:00,000 --> 00:00:04,000
// 下午好，今晚又发布了野火疏散警报。

// 2
// 00:00:04,000 --> 00:00:08,199
// 北极光县通知霍克希尔斯的居民，该地靠近双湖省立公园，

// 3
// 00:00:08,199 --> 00:00:12,760
// 需随时做好撤离准备。阿尔伯塔省北部的消防员们

// 4
// 00:00:12,760 --> 00:00:16,079
// 一直在努力控制几处失控的野火，

// 5
// 00:00:16,079 --> 00:00:20,280
// 这是一场艰苦的战斗，一个社区失去了数十座建筑。

// 6
// 00:00:20,280 --> 00:00:24,920
// 英格尔伍德社区的居民说，他们醒来时看到的场景

// 7
// 00:00:24,920 --> 00:00:29,399
// 感觉就像电影里一样，一名可能受损的司机肆意破坏，

// 8
// 00:00:29,399 --> 00:00:33,900
// 损坏了多辆汽车。贾斯敏·金将进行解释。

// 9
// 00:00:33,900 --> 00:00:38,599
// 一名男子在市中心一处建筑工地被发现死亡后，凶杀案调查组已被召集进行调查，

// 10
// 00:00:38,599 --> 00:00:43,799
// 这影响了市中心另一条主要路线。107大道东西向车道已封闭。

// 11
// 00:00:43,799 --> 00:00:49,099
// 据丽莎·麦格雷戈报道，现在市中心每条主要路线上都设置了路障。

// 12
// 00:00:49,099 --> 00:00:53,479
// 48小时内，油人队将主场迎战斯坦利杯决赛的第一场比赛，

// 13
// 00:00:53,500 --> 00:00:58,080
// 寻求对佛罗里达黑豹队的复仇，上赛季黑豹队以七场比赛击败了他们。

// 14
// 00:00:58,080 --> 00:01:02,919
// 斯莱普·科尼克现在在演播室与我连线。加拿大总理们今天向总理提出了项目，

// 15
// 00:01:02,919 --> 00:01:07,360
// 包括丹妮尔·史密斯，她正在推动一条输油管道。摩根·布莱克报道。

// 16
// 00:01:07,360 --> 00:01:14,279
// 包括丹妮尔·史密斯，她正在推动一条输油管道。摩根·布莱克报道。`
//     });




    // 流水线状态消息
    const uploadStatusMessages = ref([]);
    const generationStatusMessages = ref([]);

    // 生成状态
    const isGenerating = ref(false);
    const isGenerationCompleted = ref(false); // 生成结束状态
    const isGenerationFailed = ref(false); // 生成失败状态

    // SSE事件管理器和状态
    const sseEventManager = new window.SSEEventManager();
    // 设置为全局变量，供utils.js使用
    window.sseEventManagerInstance = sseEventManager;
    const sseStatus = ref({
        connection: {
            isConnected: false,
            connectionId: null,
            startTime: null,
            lastHeartbeat: null,
            errorCount: 0
        },
        pipeline: {
            name: null,
            status: null,
            progress: 0,
            startTime: null,
            endTime: null,
            errorDetails: null
        },
        currentTask: {
            name: null,
            status: null,
            detail: null,
            startTime: null,
            errorInfo: null
        },
        taskHistory: []
    });

    // 视频配置参数状态
    const videoConfig = ref({
        repeatCount: 3,
        repeatModes: RepeatModeManager.getDefaultModeSequence(3),
        backgroundStyle: "newspaper", // 背景风格配置：newspaper(报纸风格) 或 abstract(抽象风格)
        subtitleConfig: {
            videoGuide: {
                enabled: true,
                title1: "坚持30天",
                title2: "听懂国外新闻"
            },
            advertisement: {
                enabled: true,
                titles: [
                    {
                        line1: "🌍关注水蜜桃英语，",
                        line2: "每天2分钟，听全球要闻！"
                    },
                    {
                        line1: "🌍关注水蜜桃英语，",
                        line2: "每天2分钟，听力大提升！"
                    },
                    {
                        line1: "🌍关注水蜜桃英语，",
                        line2: "不靠字幕，听懂世界声！"
                    }
                ]
            }
        }
    });

    // 添加详细的初始化日志
    console.log('[DEBUG][videoConfig][初始化] videoConfig 初始状态:');
    console.log('[DEBUG][videoConfig][初始化] - repeatCount:', videoConfig.value.repeatCount);
    console.log('[DEBUG][videoConfig][初始化] - backgroundStyle:', videoConfig.value.backgroundStyle);
    console.log('[DEBUG][videoConfig][初始化] - repeatModes:', JSON.stringify(videoConfig.value.repeatModes, null, 2));
    console.log('[DEBUG][videoConfig][初始化] - subtitleConfig 完整结构:', JSON.stringify(videoConfig.value.subtitleConfig, null, 2));
    console.log('[DEBUG][videoConfig][初始化] - videoGuide.enabled:', videoConfig.value.subtitleConfig.videoGuide.enabled);
    console.log('[DEBUG][videoConfig][初始化] - videoGuide.title1:', videoConfig.value.subtitleConfig.videoGuide.title1);
    console.log('[DEBUG][videoConfig][初始化] - videoGuide.title2:', videoConfig.value.subtitleConfig.videoGuide.title2);
    console.log('[DEBUG][videoConfig][初始化] - advertisement.enabled:', videoConfig.value.subtitleConfig.advertisement.enabled);
    console.log('[DEBUG][videoConfig][初始化] - advertisement.titles 数量:', videoConfig.value.subtitleConfig.advertisement.titles.length);

    // 注册SSE事件管理器的状态更新回调
    sseEventManager.registerStatusUpdateCallback((statusData) => {
        console.log('[DEBUG][SSE][状态更新] 收到SSE状态更新:', statusData);
        sseStatus.value = statusData;

        // 只有视频生成相关的流水线才同步生成状态
        const isVideoGenerationPipeline = statusData.pipeline.name &&
            statusData.pipeline.name.toLowerCase().includes('generation');

        if (isVideoGenerationPipeline) {
            console.log('[DEBUG][SSE][状态更新] 检测到视频生成流水线，同步生成状态');
            if (statusData.pipeline.status === 'completed') {
                isGenerationCompleted.value = true;
                isGenerationFailed.value = false;
                isGenerating.value = false;
            } else if (statusData.pipeline.status === 'failed') {
                isGenerationFailed.value = true;
                isGenerationCompleted.value = false;
                isGenerating.value = false;
            } else if (statusData.pipeline.status === 'running') {
                isGenerating.value = true;
                isGenerationCompleted.value = false;
                isGenerationFailed.value = false;
            }
        } else {
            console.log('[DEBUG][SSE][状态更新] 非视频生成流水线，不同步生成状态，流水线名称:', statusData.pipeline.name);
        }
    });

    return {
        // 界面状态
        activeSection,
        isUploading,
        selectedFile,
        isExitingEdit, // 🆕 退出编辑中状态

        // 项目选择状态
        projectList,
        selectedProject,
        isLoadingProjects,
        projectLoadError,
        projectPagination,
        showProjectSelectionDialog,  // 🆕 项目选择对话框显示状态
        dialogSelectedProject,       // 🆕 对话框中选中的项目

        // 视频编辑状态
        editorData,

        // 流水线状态
        uploadStatusMessages,
        generationStatusMessages,

        // 生成状态
        isGenerating,
        isGenerationCompleted,
        isGenerationFailed,

        // 下载链接状态
        downloadLinks,

        // 视频配置参数
        videoConfig,

        // SSE状态
        sseStatus,

        // 生成视频查看对话框状态
        showGeneratedVideosDialog,
        generatedVideosList,
        selectedVideos,
        selectAllVideos,
        currentProjectId,
        loadingGeneratedVideos,
        videoStatusMap,
        markingAsPublished,
        batchDownloading,
        generatingLiveVideoDraft
    };
}



// ============================================================================
// 3. 计算属性模块
// ============================================================================

/**
 * @功能概述: 创建应用计算属性
 * @参数说明: {object} state - 应用状态对象
 * @返回值: {object} 包含所有计算属性的对象
 */
function createComputedProperties(state, videoEditorState) {
    /**
     * @功能概述: 编辑状态指示文本
     * @计算逻辑: 根据上传和编辑状态返回相应的指示文本
     */
    const editingIndicatorText = computed(() => {
        if (state.isUploading.value) return '处理中...';
        if (state.activeSection.value === 'video-editor' && state.editorData.value) return '视频编辑中';
        return '处理中...';
    });



    /**
     * @功能概述: 参数设置按钮启用状态
     * @计算逻辑: 检查片段和裁剪参数是否完整
     */
    const isParameterSettingsEnabled = computed(() => {
        // 检查片段数据：必须有至少一个完整片段
        const validSegments = videoEditorState.clipSegments.value.filter(s =>
            s.startTime !== null && s.startTime !== undefined &&
            s.endTime !== null && s.endTime !== undefined
        );

        // 检查裁剪参数：必须已确认且包含完整数据
        const cropParamsValid = videoEditorState.confirmedCropParams.value.isConfirmed === true &&
            typeof videoEditorState.confirmedCropParams.value.x === 'number' &&
            typeof videoEditorState.confirmedCropParams.value.y === 'number' &&
            typeof videoEditorState.confirmedCropParams.value.width === 'number' &&
            typeof videoEditorState.confirmedCropParams.value.height === 'number';

        return validSegments.length > 0 && cropParamsValid;
    });

    /**
     * @功能概述: 生成确认按钮启用状态
     * @计算逻辑: 检查所有前置条件是否满足
     */
    const isGenerationEnabled = computed(() => {
        // 复用参数设置的检查逻辑
        const basicRequirementsMet = isParameterSettingsEnabled.value;

        // 检查视频配置参数：必须有repeatCount
        const videoConfigValid = state.videoConfig.value.repeatCount > 0 &&
            state.videoConfig.value.repeatModes.length > 0;

        return basicRequirementsMet && videoConfigValid;
    });



    /**
     * @功能概述: 获取当前时间字符串
     * @返回值: {string} 格式化的时间字符串
     */
    const getCurrentTime = () => {
        return new Date().toLocaleTimeString();
    };

    return {
        editingIndicatorText,
        isParameterSettingsEnabled,
        isGenerationEnabled,
        getCurrentTime,
    };
}

// ============================================================================
// 4. 文件上传模块
// ============================================================================

/**
 * @功能概述: 创建文件上传相关功能
 * @参数说明: {object} state - 应用状态对象
 * @返回值: {object} 包含文件上传方法的对象
 */
function createUploadFunctions(state) {
    const logPrefix = '[文件：app.js][createUploadFunctions]';

    // 创建一个隐藏的 input[type=file] 元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'video/*,.srt'; // 接受视频和SRT文件
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);

    

    // 绑定文件选择事件到DOM元素
    fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;

        console.log(`${logPrefix} 选择的文件:`, file.name, file.type);

        // 直接赋值给selectedFile，让前端能够显示文件名
        state.selectedFile.value = file;
    });

    const setFileInputRef = (ref) => {
        // 这个函数保持兼容性，但在这个实现中不需要使用
        console.log(`${logPrefix} setFileInputRef called with:`, ref);
    };

    /**
     * @功能概述: 触发文件选择对话框
     */
    const triggerFileInput = () => {
        console.log(`${logPrefix} 触发文件选择`);
        fileInput.click();
    };



    /**
     * @功能概述: 执行视频文件上传
     * @调用关系: 由模板中的"开始上传"按钮调用
     * @状态影响: 更新isUploading、uploadStatusMessages、editorData等状态
     * @流程说明: 上传文件 → 进入流水线 → 处理结果 → 赋值editorData → 切换到video-editor
     */
    const uploadVideo = async () => {
        const logPrefix = '[文件：app.js][Vue App][uploadVideo]';

        if (!state.selectedFile.value) {
            console.warn(`${logPrefix} 没有选择文件，无法上传`);
            ElMessage.warning('请先选择一个视频文件');
            return;
        }

        console.log(`${logPrefix} 开始上传视频文件: ${state.selectedFile.value.name}`);
        state.isUploading.value = true;
        state.uploadStatusMessages.value = [];

        try {
            // 创建FormData对象
            const formData = new FormData();
            formData.append('videoFile', state.selectedFile.value); // 修改字段名以匹配uploadMiddleware

            console.log(`${logPrefix} 调用上传API: /api/video/uploadVideo`);

            // 使用增强的uploadApi（支持SSE）调用上传，进入流水线处理
            const result = await window.Utils.uploadApi('/api/video/uploadVideo', formData);
            console.log(`${logPrefix} SSE流水线处理完成，结果:`, result);

            // 检查流水线处理结果
            if (result && result.status === 'completed') {
                console.log(`${logPrefix} 流水线执行成功，开始赋值editorData`);

                // 提取上下文信息 - 支持多种结果结构
                let finalContext = null;
                let videoPlaybackUrl = null;
                let videoIdentifier = null;
                let originalVideoPath = null;
                let originalVideoName = null;
                let englishSrtContent = null;
                // 移除：let chineseSrtContent = null;

                // 方式1：从标准化结果的context字段获取
                if (result.context) {
                    finalContext = result.context;
                    videoPlaybackUrl = result.context.videoPlaybackUrl;
                    videoIdentifier = result.context.videoIdentifier;
                    originalVideoPath = result.context.originalVideoPath;
                    originalVideoName = result.context.originalVideoName;
                    // 新的字幕数据字段名
                    englishSrtContent = result.context.optimizedEnglishSrtContent;
                    // 移除：chineseSrtContent = result.context.chineseSrtContent;
                }

                // 方式2：从finalContextPreview字段解析（进度事件中的预览）
                if (!finalContext && result.finalContextPreview) {
                    try {
                        finalContext = JSON.parse(result.finalContextPreview);
                        videoPlaybackUrl = finalContext.videoPlaybackUrl;
                        videoIdentifier = finalContext.videoIdentifier;
                        originalVideoPath = finalContext.originalVideoPath;
                        originalVideoName = finalContext.originalVideoName;
                        englishSrtContent = finalContext.optimizedEnglishSrtContent;
                        // 移除：chineseSrtContent = finalContext.chineseSrtContent;
                        console.log(`${logPrefix} 从finalContextPreview解析到context:`, finalContext);
                    } catch (e) {
                        console.warn(`${logPrefix} 解析finalContextPreview失败:`, e);
                    }
                }

                // 方式3：从result根级别获取（兼容性）
                if (!videoPlaybackUrl) {
                    videoPlaybackUrl = result.videoPlaybackUrl;
                }
                if (!videoIdentifier) {
                    videoIdentifier = result.videoIdentifier;
                }
                if (!originalVideoPath) {
                    originalVideoPath = result.originalVideoPath;
                }
                if (!originalVideoName) {
                    originalVideoName = result.originalVideoName;
                }
                if (!englishSrtContent) {
                    englishSrtContent = result.optimizedEnglishSrtContent;
                }

                console.log(`${logPrefix} 提取的数据:`, {
                    videoPlaybackUrl,
                    videoIdentifier,
                    originalVideoPath,
                    originalVideoName,
                    englishSrtContent: englishSrtContent ? '已获取' : '未获取',
                    // 移除：chineseSrtContent: chineseSrtContent ? '已获取' : '未获取',
                    resultStructure: {
                        hasContext: !!result.context,
                        contextKeys: result.context ? Object.keys(result.context) : [],
                        finalContextKeys: finalContext ? Object.keys(finalContext) : []
                    }
                });

                // 如果没有获取到videoPlaybackUrl，但有originalVideoPath，尝试拼接web链接
                if (!videoPlaybackUrl && originalVideoPath) {
                    // 拼接web链接：http://localhost:8081/backend/uploads/videoFile-1749951452896-516603124.mp4
                    const baseUrl = window.location.protocol + '//' + window.location.hostname + ':8081';

                    console.log(`${logPrefix} 原始路径: ${originalVideoPath}`);

                    // 处理路径：从Windows绝对路径中提取相对路径部分
                    let webPath = originalVideoPath;

                    // 将反斜杠转换为正斜杠（Windows路径兼容）
                    webPath = webPath.replace(/\\/g, '/');

                    // 提取backend/uploads/之后的部分
                    if (webPath.includes('/backend/uploads/')) {
                        // 从/backend/uploads/开始提取
                        webPath = webPath.substring(webPath.indexOf('/backend/uploads/') + 1); // +1是为了去掉开头的/
                    } else if (webPath.includes('backend/uploads/')) {
                        // 从backend/uploads/开始提取
                        webPath = webPath.substring(webPath.indexOf('backend/uploads/'));
                    } else if (webPath.includes('uploads/')) {
                        // 如果只有uploads/，补充backend前缀
                        const uploadsIndex = webPath.indexOf('uploads/');
                        webPath = 'backend/' + webPath.substring(uploadsIndex);
                    } else {
                        // 如果路径格式不符合预期，尝试提取文件名并构造路径
                        const fileName = webPath.substring(webPath.lastIndexOf('/') + 1);
                        if (fileName && videoIdentifier) {
                            webPath = `backend/uploads/${videoIdentifier}/${fileName}`;
                        } else {
                            console.warn(`${logPrefix} 无法从路径中提取有效的web路径: ${originalVideoPath}`);
                            webPath = null;
                        }
                    }

                    if (webPath) {
                        videoPlaybackUrl = `${baseUrl}/${webPath}`;
                        console.log(`${logPrefix} 拼接的videoPlaybackUrl: ${videoPlaybackUrl}`);
                    } else {
                        console.warn(`${logPrefix} 无法构造有效的videoPlaybackUrl`);
                    }
                }

                // 赋值给editorData（只保留英文字幕）
                state.editorData.value = {
                    // 视频播放URL（web链接）
                    videoPlaybackUrl: videoPlaybackUrl || null,
                    // 视频标识符
                    videoIdentifier: videoIdentifier || null,
                    // 原始视频文件路径
                    originalVideoPath: originalVideoPath || null,
                    // 原始视频文件名
                    originalVideoName: originalVideoName || state.selectedFile.value.name,
                    // 英文字幕内容（SRT格式）
                    englishSrtContent: englishSrtContent || null,
                    // 移除：chineseSrtContent: chineseSrtContent || null
                };

                console.log(`${logPrefix} editorData赋值完成:`, state.editorData.value);

                // 验证所有必要字段都已赋值
                const requiredFields = ['videoIdentifier', 'videoPlaybackUrl', 'originalVideoName'];
                const missingFields = requiredFields.filter(field => !state.editorData.value[field]);

                if (missingFields.length > 0) {
                    console.warn(`${logPrefix} 警告：以下字段缺失: ${missingFields.join(', ')}`);
                    state.uploadStatusMessages.value.push(`⚠️ 部分数据缺失: ${missingFields.join(', ')}`);
                } else {
                    console.log(`${logPrefix} 所有必要字段都已正确赋值`);
                }

                // 成功后切换到video-editor状态
                state.activeSection.value = 'video-editor';
                console.log(`${logPrefix} 已切换到video-editor状态`);

                // 重新初始化视频编辑器数据（重要：确保所有数据正确同步）
                setTimeout(() => {
                    const logPrefix2 = '[文件：app.js][Vue App][uploadVideo][initializeData]';
                    console.log(`${logPrefix2} 开始初始化视频编辑器数据`);

                    // 触发全局初始化事件，让Vue组件内部的initializeVideoEditorData函数执行
                    const initEvent = new CustomEvent('initializeVideoEditor', {
                        detail: {
                            editorData: state.editorData.value,
                            timestamp: Date.now()
                        }
                    });
                    window.dispatchEvent(initEvent);

                    console.log(`${logPrefix2} 已触发视频编辑器初始化事件`);
                }, 100); // 延迟100ms确保DOM更新完成

                // 显示成功消息
                ElMessage.success('🎉 视频上传和处理完成！');
                state.uploadStatusMessages.value.push('✅ 视频处理完成，已进入编辑模式');

            } else if (result && result.status === 'failed') {
                // 流水线处理失败
                const errorMsg = result.error ? result.error.message : '流水线处理失败';
                console.error(`${logPrefix} 流水线处理失败: ${errorMsg}`);
                ElMessage.error(`流水线处理失败: ${errorMsg}`);
                state.uploadStatusMessages.value.push(`❌ 流水线处理失败: ${errorMsg}`);
            } else {
                // 结果格式异常
                console.error(`${logPrefix} 流水线结果格式异常:`, result);
                ElMessage.error('流水线结果格式异常');
                state.uploadStatusMessages.value.push('❌ 流水线结果格式异常');
            }

        } catch (error) {
            console.error(`${logPrefix} 上传失败:`, error);
            ElMessage.error(`上传失败: ${error.message}`);
            state.uploadStatusMessages.value.push(`❌ 上传失败: ${error.message}`);
        } finally {
            state.isUploading.value = false;
        }
    };

    return {
        setFileInputRef,
        triggerFileInput,
        uploadVideo
    };
}

// ============================================================================
// 5. 导航控制模块
// ============================================================================

/**
 * @功能概述: 创建导航控制功能
 * @参数说明: {object} state - 应用状态对象
 * @返回值: {object} 包含导航方法的对象
 */
function createNavigationFunctions(state) {
    /**
     * @功能概述: 导航到指定功能区域
     * @调用关系: 由导航按钮调用
     * @状态影响: 更新activeSection状态
     * @参数说明: {string} section - 目标功能区域名称
     */
    const navigateTo = (section) => {
        const logPrefix = '[文件：app.js][Vue App][navigateTo]';
        console.log(`${logPrefix} 导航到功能区域: ${section}`);
        state.activeSection.value = section;
    };

    return {
        navigateTo
    };
}

// ============================================================================
// 5.5. 项目管理模块
// ============================================================================

/**
 * @功能概述: 创建项目管理相关功能
 * @参数说明: {object} state - 应用状态对象
 * @返回值: {object} 包含项目管理方法的对象
 * @职责范围:
 *   - 项目列表获取和展示
 *   - 项目选择和确认
 *   - 项目详情获取
 *   - 与编辑器的数据对接
 */
function createProjectManagementFunctions(state) {
    const logPrefix = '[文件：app.js][createProjectManagementFunctions]';

    /**
     * @功能概述: 初始化项目列表模块模板
     * @调用关系: 由navigateTo函数在切换到项目列表时调用
     * @状态影响: 动态加载项目列表HTML模板到容器中
     */
    const initializeProjectListModule = async () => {
        const functionLogPrefix = `${logPrefix}[initializeProjectListModule]`;

        try {
            console.log(`${functionLogPrefix} 开始初始化项目列表模块`);

            const container = document.getElementById('project-list-container');
            if (!container) {
                console.warn(`${functionLogPrefix} 项目列表容器不存在`);
                return;
            }

            // 检查是否已经初始化过
            if (container.querySelector('.project-grid')) {
                console.log(`${functionLogPrefix} 项目列表模块已初始化，跳过`);
                return;
            }

            // 创建ProjectListModule实例并初始化
            if (window.ProjectListModule) {
                const module = new window.ProjectListModule(container, {
                    vueState: state,
                    loadTemplate: true
                });

                await module.init();
                console.log(`${functionLogPrefix} 项目列表模块初始化完成`);
            } else {
                console.warn(`${functionLogPrefix} ProjectListModule类不存在`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} 初始化项目列表模块失败:`, error);
        }
    };

    /**
     * @功能概述: 加载项目列表
     * @参数说明: {object} options - 查询选项（分页、排序等）
     * @调用关系: 由"选择已有视频"功能调用
     * @状态影响: 更新projectList、projectPagination、isLoadingProjects等状态
     */
    const loadProjectList = async (options = {}) => {
        const functionLogPrefix = `${logPrefix}[loadProjectList]`;

        try {
            console.log(`${functionLogPrefix} 开始加载项目列表，选项:`, options);

            // 设置加载状态
            state.isLoadingProjects.value = true;
            state.projectLoadError.value = null;

            // 调用API获取项目列表
            const response = await window.Utils.fetchProjectList(options);

            if (response.status === 'success') {
                // 更新项目列表数据
                state.projectList.value = response.data.projects || [];
                state.projectPagination.value = response.data.pagination || {
                    page: 1, pageSize: 10, total: 0, totalPages: 0
                };

                console.log(`${functionLogPrefix} 项目列表加载成功，数量: ${state.projectList.value.length}`);
                console.log(`${functionLogPrefix} 分页信息:`, state.projectPagination.value);

                // 显示成功消息
                ElMessage.success(`✅ 加载了 ${state.projectList.value.length} 个项目`);

            } else {
                throw new Error(response.message || '获取项目列表失败');
            }

        } catch (error) {
            console.error(`${functionLogPrefix} 加载项目列表失败:`, error);

            // 设置错误状态
            state.projectLoadError.value = error.message;
            state.projectList.value = [];

            // 显示错误消息
            ElMessage.error(`❌ 加载项目列表失败: ${error.message}`);

        } finally {
            // 清除加载状态
            state.isLoadingProjects.value = false;
        }
    };

    /**
     * @功能概述: 选择项目 - 打开项目选择确认对话框
     * @参数说明: {object} project - 选中的项目对象
     * @调用关系: 由项目卡片的选择按钮调用
     * @状态影响: 更新dialogSelectedProject状态，显示对话框
     */
    const selectProject = (project) => {
        const functionLogPrefix = `${logPrefix}[selectProject]`;

        console.log(`${functionLogPrefix} 打开项目选择对话框:`, project.originalVideoName);
        console.log(`${functionLogPrefix} 项目ID: ${project.videoIdentifier}`);

        // 设置对话框中的选中项目
        state.dialogSelectedProject.value = project;

        // 显示项目选择对话框
        state.showProjectSelectionDialog.value = true;

        console.log(`${functionLogPrefix} 项目选择对话框已打开`);
    };

    /**
     * @功能概述: 重置项目选择相关状态和参数
     * @调用关系: 由对话框关闭、取消选择等操作调用
     * @状态影响: 重置所有项目选择相关的状态到初始值
     */
    const resetProjectSelectionStates = () => {
        const functionLogPrefix = `${logPrefix}[resetProjectSelectionStates]`;

        console.log(`${functionLogPrefix} 开始重置项目选择相关状态`);

        // 重置主要选择状态
        state.selectedProject.value = null;
        state.dialogSelectedProject.value = null;

        // 重置对话框显示状态
        state.showProjectSelectionDialog.value = false;

        console.log(`${functionLogPrefix} 项目选择状态重置完成`);
    };

    /**
     * @功能概述: 取消项目选择 - 关闭对话框并重置状态
     * @调用关系: 由对话框的取消按钮调用
     * @状态影响: 关闭对话框，重置所有相关状态
     */
    const clearProjectSelection = () => {
        const functionLogPrefix = `${logPrefix}[clearProjectSelection]`;

        console.log(`${functionLogPrefix} 取消项目选择，关闭对话框`);

        // 重置所有项目选择相关状态
        resetProjectSelectionStates();

        ElMessage.info('📋 已取消项目选择');
    };

    /**
     * @功能概述: 确认项目选择 - 从对话框确认选择并进入编辑器
     * @调用关系: 由对话框的"开始编辑"按钮调用
     * @状态影响: 更新selectedProject和editorData状态，切换到video-editor界面
     * @流程说明: 获取项目详情 → 填充editorData → 切换界面 → 初始化编辑器 → 关闭对话框
     */
    const confirmProjectSelection = async () => {
        const functionLogPrefix = `${logPrefix}[confirmProjectSelection]`;

        try {
            if (!state.dialogSelectedProject.value) {
                throw new Error('请先选择一个项目');
            }

            const project = state.dialogSelectedProject.value;
            console.log(`${functionLogPrefix} 确认选择项目: ${project.originalVideoName}`);

            // 将对话框中的选择应用到主状态
            state.selectedProject.value = project;
            console.log(`${functionLogPrefix} 开始获取项目详情: ${project.videoIdentifier}`);

            // 显示加载提示
            const loading = ElLoading.service({
                lock: true,
                text: '正在加载项目数据...',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            try {
                // 获取项目详细信息
                const response = await window.Utils.fetchProjectDetails(project.videoIdentifier);

                if (response.status === 'success' && response.data?.editorData) {
                    const editorData = response.data.editorData;

                    console.log(`${functionLogPrefix} 项目详情获取成功`);
                    console.log(`${functionLogPrefix} editorData字段:`, Object.keys(editorData));

                    // 验证数据完整性
                    const requiredFields = ['videoIdentifier', 'videoPlaybackUrl', 'originalVideoName'];
                    const missingFields = requiredFields.filter(field => !editorData[field]);

                    if (missingFields.length > 0) {
                        console.warn(`${functionLogPrefix} 警告：以下字段缺失: ${missingFields.join(', ')}`);
                        ElMessage.warning(`⚠️ 部分数据缺失: ${missingFields.join(', ')}`);
                    } else {
                        console.log(`${functionLogPrefix} 所有必要字段都已正确获取`);
                    }

                    // 将editorData赋值给状态（与uploadVideo函数中的格式完全一致）
                    state.editorData.value = {
                        // 视频播放URL（web链接）
                        videoPlaybackUrl: editorData.videoPlaybackUrl || null,
                        // 视频标识符
                        videoIdentifier: editorData.videoIdentifier || null,
                        // 原始视频文件路径
                        originalVideoPath: editorData.originalVideoPath || null,
                        // 原始视频文件名
                        originalVideoName: editorData.originalVideoName || null,
                        // 英文字幕内容（SRT格式）
                        englishSrtContent: editorData.englishSrtContent || null,
                        // 额外的URL信息（供前端使用）
                        audioFileUrl: editorData.audioFileUrl || null,
                        transcriptionJsonUrl: editorData.transcriptionJsonUrl || null,
                        englishSrtUrl: editorData.englishSrtUrl || null,
                        chineseSrtUrl: editorData.chineseSrtUrl || null
                    };

                    console.log(`${functionLogPrefix} editorData赋值完成:`, state.editorData.value);

                    // 切换到video-editor状态
                    state.activeSection.value = 'video-editor';
                    console.log(`${functionLogPrefix} 已切换到video-editor状态`);

                    // 重新初始化视频编辑器数据（与uploadVideo函数保持一致）
                    setTimeout(() => {
                        const initLogPrefix = `${functionLogPrefix}[initializeData]`;
                        console.log(`${initLogPrefix} 开始初始化视频编辑器数据`);

                        // 触发全局初始化事件，让Vue组件内部的initializeVideoEditorData函数执行
                        const initEvent = new CustomEvent('initializeVideoEditor', {
                            detail: {
                                editorData: state.editorData.value,
                                timestamp: Date.now(),
                                source: 'projectSelection' // 标识数据来源
                            }
                        });
                        window.dispatchEvent(initEvent);

                        console.log(`${initLogPrefix} 已触发视频编辑器初始化事件`);
                    }, 100); // 延迟100ms确保DOM更新完成

                    // 显示成功消息
                    ElMessage.success('🎉 项目加载完成，已进入编辑模式！');

                    // 关闭对话框并重置状态
                    state.showProjectSelectionDialog.value = false;
                    state.dialogSelectedProject.value = null;

                } else {
                    throw new Error(response.message || '项目数据格式异常');
                }

            } finally {
                loading.close();
            }

        } catch (error) {
            console.error(`${functionLogPrefix} 确认选择失败:`, error);
            ElMessage.error(`❌ 加载项目失败: ${error.message}`);
        }
    };

    /**
     * @功能概述: 重命名项目文件
     * @参数说明: {object} project - 要重命名的项目对象
     * @调用关系: 由项目卡片的重命名按钮调用
     * @状态影响: 更新项目列表中对应项目的文件名
     */
    const renameProject = async (project) => {
        const functionLogPrefix = `${logPrefix}[renameProject]`;

        try {
            console.log(`${functionLogPrefix} 准备重命名项目:`, project.originalVideoName);
            console.log(`${functionLogPrefix} 项目ID: ${project.videoIdentifier}`);

            // 获取当前文件名（去掉扩展名）
            const currentFileName = project.originalVideoName;
            const fileExtension = currentFileName.substring(currentFileName.lastIndexOf('.'));
            const currentBaseName = currentFileName.substring(0, currentFileName.lastIndexOf('.'));

            // 显示输入对话框
            const { value: newFileName } = await ElementPlus.ElMessageBox.prompt(
                '请输入新的文件名（不包含扩展名）：',
                '重命名项目文件',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputValue: currentBaseName,
                    inputValidator: (value) => {
                        if (!value || value.trim() === '') {
                            return '文件名不能为空';
                        }
                        if (value.includes('<') || value.includes('>') || value.includes(':') ||
                            value.includes('"') || value.includes('/') || value.includes('\\') ||
                            value.includes('|') || value.includes('?') || value.includes('*')) {
                            return '文件名不能包含以下字符: < > : " / \\ | ? *';
                        }
                        return true;
                    }
                }
            );

            if (!newFileName || newFileName.trim() === '') {
                console.log(`${functionLogPrefix} 用户取消重命名操作`);
                return;
            }

            const cleanFileName = newFileName.trim();

            // 检查是否与当前文件名相同
            if (cleanFileName === currentBaseName) {
                ElementPlus.ElMessage.info('文件名未发生变化');
                return;
            }

            console.log(`${functionLogPrefix} 新文件名: ${cleanFileName}`);

            // 调用重命名API
            const response = await fetch('/api/video/renameProject', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    videoIdentifier: project.videoIdentifier,
                    newFileName: cleanFileName
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                // 重新加载项目列表以获取最新数据
                await loadProjectList();

                ElementPlus.ElMessage.success(`📝 文件重命名成功: ${result.data.fullFileName}`);
                console.log(`${functionLogPrefix} 文件重命名成功: ${currentFileName} -> ${result.data.fullFileName}`);
            } else {
                throw new Error(result.message || '重命名文件失败');
            }

        } catch (error) {
            if (error.message === 'cancel') {
                console.log(`${functionLogPrefix} 用户取消重命名操作`);
                return;
            }

            console.error(`${functionLogPrefix} 重命名文件失败:`, error);
            ElementPlus.ElMessage.error(`重命名文件失败: ${error.message}`);
        }
    };

    /**
     * @功能概述: 删除项目
     * @参数说明: {object} project - 要删除的项目对象
     * @调用关系: 由项目卡片的删除按钮调用
     * @状态影响: 删除项目后重新加载项目列表
     */
    const deleteProject = async (project) => {
        const functionLogPrefix = `${logPrefix}[deleteProject]`;

        try {
            console.log(`${functionLogPrefix} 准备删除项目:`, project.originalVideoName);
            console.log(`${functionLogPrefix} 项目ID: ${project.videoIdentifier}`);

            // 确认删除操作
            const confirmResult = await ElementPlus.ElMessageBox.confirm(
                `确定要删除项目 "${project.originalVideoName}" 吗？\n\n此操作将永久删除项目的所有文件，无法恢复。`,
                '确认删除',
                {
                    confirmButtonText: '确定删除',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: false
                }
            );

            if (confirmResult !== 'confirm') {
                console.log(`${functionLogPrefix} 用户取消删除操作`);
                return;
            }

            console.log(`${functionLogPrefix} 用户确认删除，开始发送删除请求`);

            // 发送删除请求
            const response = await fetch('/api/video/deleteProject', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    videoIdentifier: project.videoIdentifier
                })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                console.log(`${functionLogPrefix} 项目删除成功:`, result);

                ElMessage.success(`✅ 项目 "${project.originalVideoName}" 删除成功`);

                // 如果删除的是当前选中的项目，清除选择
                if (state.selectedProject && state.selectedProject.videoIdentifier === project.videoIdentifier) {
                    console.log(`${functionLogPrefix} 删除的是当前选中项目，清除选择状态`);
                    clearProjectSelection();
                }

                // 重新加载项目列表
                console.log(`${functionLogPrefix} 重新加载项目列表`);
                await loadProjectList();

            } else {
                console.error(`${functionLogPrefix} 删除失败:`, result);
                ElMessage.error(`❌ 删除失败: ${result.message || '未知错误'}`);
            }

        } catch (error) {
            if (error.message === 'cancel') {
                console.log(`${functionLogPrefix} 用户取消删除操作`);
                return;
            }

            console.error(`${functionLogPrefix} 删除项目失败:`, error);
            ElMessage.error(`❌ 删除项目失败: ${error.message}`);
        }
    };

    /**
     * @功能概述: 获取视频发布状态
     * @参数说明: {string} projectId - 项目唯一标识符
     * @调用关系: 由openGeneratedVideosDialog调用
     * @状态影响: 更新videoStatusMap状态
     */
    const loadVideoStatus = async (projectId) => {
        const functionLogPrefix = `${logPrefix}[loadVideoStatus]`;

        try {
            console.log(`${functionLogPrefix} 开始获取视频发布状态，项目ID: ${projectId}`);

            const response = await fetch(`http://localhost:8081/api/video/getVideoTags/${projectId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`${functionLogPrefix} 获取视频状态成功:`, result);

            // 清空现有状态映射
            state.videoStatusMap.value.clear();

            // 解析API返回的状态数据
            if (result.success && result.data && result.data.status) {
                Object.entries(result.data.status).forEach(([filename, statusData]) => {
                    state.videoStatusMap.value.set(filename, statusData.status || 'draft');
                });
                console.log(`${functionLogPrefix} 状态映射更新完成，共${state.videoStatusMap.value.size}个视频`);
            }

        } catch (error) {
            console.warn(`${functionLogPrefix} 获取视频状态失败:`, error);
            // 不显示错误消息，因为这不是关键功能
            state.videoStatusMap.value.clear();
        }
    };

    /**
     * @功能概述: 打开生成视频查看对话框
     * @参数说明: {string} projectId - 项目唯一标识符
     * @调用关系: 由项目卡片的"查看生成短视频"按钮调用
     * @状态影响: 更新showGeneratedVideosDialog、generatedVideosList等状态
     */
    const openGeneratedVideosDialog = async (projectId) => {
        const functionLogPrefix = `${logPrefix}[openGeneratedVideosDialog]`;

        try {
            console.log(`${functionLogPrefix} 打开生成视频对话框，项目ID: ${projectId}`);

            // 设置当前项目ID并显示对话框
            state.currentProjectId.value = projectId;
            state.showGeneratedVideosDialog.value = true;
            state.loadingGeneratedVideos.value = true;

            // 重置选中状态
            state.selectedVideos.value.clear();
            state.selectAllVideos.value = false;

            // 并行调用API获取生成的视频列表和发布状态
            const [result] = await Promise.all([
                Utils.callListGeneratedFilesAPI(projectId, {
                    limit: 50, // 获取更多数据
                    sortBy: 'time_desc' // 按时间降序排列
                }),
                loadVideoStatus(projectId) // 获取视频发布状态
            ]);

            if (result.success) {
                console.log(`${functionLogPrefix} 获取生成视频列表成功:`, result.data);
                state.generatedVideosList.value = result.data.pairs || [];

                ElMessage.success(`✅ 找到 ${result.data.totalVideos} 个生成的短视频`);
            } else {
                console.error(`${functionLogPrefix} 获取生成视频列表失败:`, result.error);
                state.generatedVideosList.value = [];
                ElMessage.error(`❌ 加载生成视频失败: ${result.error}`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} 打开对话框失败:`, error);
            state.generatedVideosList.value = [];
            ElMessage.error(`❌ 打开对话框失败: ${error.message}`);
        } finally {
            state.loadingGeneratedVideos.value = false;
        }
    };

    /**
     * @功能概述: 关闭生成视频查看对话框
     * @调用关系: 由对话框的关闭按钮调用
     * @状态影响: 重置所有对话框相关状态
     */
    const closeGeneratedVideosDialog = () => {
        const functionLogPrefix = `${logPrefix}[closeGeneratedVideosDialog]`;
        console.log(`${functionLogPrefix} 关闭生成视频对话框`);

        // 重置对话框显示状态
        state.showGeneratedVideosDialog.value = false;

        // 重置视频列表数据
        state.generatedVideosList.value = [];

        // 重置选中状态（创建新的空Set来触发响应式更新）
        state.selectedVideos.value = new Set();

        // 重置全选状态
        state.selectAllVideos.value = false;

        // 重置当前项目ID
        state.currentProjectId.value = null;

        // 重置加载状态
        state.loadingGeneratedVideos.value = false;

        // 重置视频状态映射
        state.videoStatusMap.value.clear();

        // 重置标注loading状态
        state.markingAsPublished.value = false;

        // 重置批量下载loading状态
        state.batchDownloading.value = false;

        // 重置生成直播视频草稿loading状态
        state.generatingLiveVideoDraft.value = false;

        console.log(`${functionLogPrefix} 所有对话框状态已重置`);
    };

    /**
     * @功能概述: 切换单个视频的选中状态
     * @参数说明: {string} videoId - 视频文件名（作为唯一标识）
     * @调用关系: 由视频列表中的checkbox调用
     * @状态影响: 更新selectedVideos和selectAllVideos状态
     */
    const toggleVideoSelection = (videoId) => {
        const functionLogPrefix = `${logPrefix}[toggleVideoSelection]`;
        console.log(`${functionLogPrefix} 切换视频选中状态: ${videoId}`);

        // 创建新的Set来触发响应式更新
        const newSelectedVideos = new Set(state.selectedVideos.value);

        if (newSelectedVideos.has(videoId)) {
            newSelectedVideos.delete(videoId);
            console.log(`${functionLogPrefix} 取消选中视频: ${videoId}`);
        } else {
            newSelectedVideos.add(videoId);
            console.log(`${functionLogPrefix} 选中视频: ${videoId}`);
        }

        // 更新响应式状态
        state.selectedVideos.value = newSelectedVideos;

        // 更新全选状态（不触发change事件）
        const totalVideos = state.generatedVideosList.value.length;
        const selectedCount = state.selectedVideos.value.size;
        const newSelectAllState = selectedCount === totalVideos && totalVideos > 0;

        // 直接更新全选状态，不触发toggleSelectAll
        state.selectAllVideos.value = newSelectAllState;

        console.log(`${functionLogPrefix} 当前选中: ${selectedCount}/${totalVideos}, 全选状态: ${newSelectAllState}`);
    };

    /**
     * @功能概述: 获取视频的发布状态
     * @参数说明: {string} filename - 视频文件名
     * @返回值: {string} 发布状态 ('published' 或 'draft')
     * @调用关系: 由Vue模板中的状态显示逻辑调用
     */
    const getVideoStatus = (filename) => {
        return state.videoStatusMap.value.get(filename) || 'draft';
    };

    /**
     * @功能概述: 获取视频状态的显示文本
     * @参数说明: {string} filename - 视频文件名
     * @返回值: {string} 显示文本
     */
    const getVideoStatusText = (filename) => {
        const status = getVideoStatus(filename);
        return status === 'published' ? '✅ 已发布' : '📝 草稿';
    };

    /**
     * @功能概述: 获取视频状态的标签类型
     * @参数说明: {string} filename - 视频文件名
     * @返回值: {string} Element Plus标签类型
     */
    const getVideoStatusType = (filename) => {
        const status = getVideoStatus(filename);
        return status === 'published' ? 'success' : 'info';
    };

    /**
     * @功能概述: 标注选中的视频为已发布
     * @调用关系: 由"标注已发布"按钮调用
     * @状态影响: 更新videoStatusMap状态，调用后端API
     */
    const markSelectedAsPublished = async () => {
        const functionLogPrefix = `${logPrefix}[markSelectedAsPublished]`;

        if (state.selectedVideos.value.size === 0) {
            ElMessage.warning('请先选择要标注的视频');
            return;
        }

        if (!state.currentProjectId.value) {
            ElMessage.error('项目ID不存在');
            return;
        }

        try {
            state.markingAsPublished.value = true;

            console.log(`${functionLogPrefix} 开始标注选中视频为已发布`);
            console.log(`${functionLogPrefix} 项目ID: ${state.currentProjectId.value}`);
            console.log(`${functionLogPrefix} 选中视频: ${Array.from(state.selectedVideos.value)}`);

            // 构建API请求数据
            const updateData = {
                videos: {}
            };

            // 为每个选中的视频设置状态为published
            state.selectedVideos.value.forEach(filename => {
                updateData.videos[filename] = {
                    status: 'published'
                };
            });

            console.log(`${functionLogPrefix} 请求数据:`, updateData);

            // 调用API更新状态
            const response = await fetch(`http://localhost:8081/api/video/updateVideoTags/${state.currentProjectId.value}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`${functionLogPrefix} API调用成功:`, result);

            if (result.success) {
                // 更新本地状态映射
                state.selectedVideos.value.forEach(filename => {
                    state.videoStatusMap.value.set(filename, 'published');
                });

                ElMessage.success(`✅ 成功标注 ${state.selectedVideos.value.size} 个视频为已发布`);
                console.log(`${functionLogPrefix} 本地状态已更新`);
            } else {
                throw new Error(result.message || '标注失败');
            }

        } catch (error) {
            console.error(`${functionLogPrefix} 标注失败:`, error);
            ElMessage.error(`❌ 标注失败: ${error.message}`);
        } finally {
            state.markingAsPublished.value = false;
        }
    };

    /**
     * @功能概述: 清理文件名，移除项目内部编码
     * @参数: filename - 原始文件名
     * @返回: 清理后的用户友好文件名
     */
    const cleanFileName = (filename) => {
        let cleanName = filename;

        // 移除项目ID前缀 (videoFile-数字-数字_)
        cleanName = cleanName.replace(/^videoFile-\d+-\d+_/, '');

        // 移除视频时间戳后缀 (_extended_video_日期时间)
        cleanName = cleanName.replace(/_extended_video_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z/, '');

        // 移除字幕时间戳后缀 (_enhanced_bilingual_subtitle_日期时间)
        cleanName = cleanName.replace(/_enhanced_bilingual_subtitle_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z/, '');

        return cleanName;
    };

    /**
     * @功能概述: 批量下载选中的视频和字幕文件
     * @调用关系: 由"批量下载"按钮调用
     * @状态影响: 更新batchDownloading状态，创建ZIP文件下载
     */
    const batchDownloadSelected = async () => {
        const functionLogPrefix = `${logPrefix}[batchDownloadSelected]`;

        if (state.selectedVideos.value.size === 0) {
            ElMessage.warning('请先选择要下载的视频');
            return;
        }

        if (!state.currentProjectId.value) {
            ElMessage.error('项目ID不存在');
            return;
        }

        // 创建单一进度消息实例
        let progressMessage = null;

        try {
            state.batchDownloading.value = true;

            console.log(`${functionLogPrefix} 开始批量下载选中视频`);
            console.log(`${functionLogPrefix} 项目ID: ${state.currentProjectId.value}`);
            console.log(`${functionLogPrefix} 选中视频: ${Array.from(state.selectedVideos.value)}`);

            // 显示初始进度消息
            progressMessage = ElMessage({
                message: '正在准备下载文件...',
                type: 'info',
                duration: 0, // 不自动关闭
                showClose: false
            });

            // 创建ZIP文件
            const zip = new JSZip();
            const baseUrl = 'http://localhost:8081/backend/uploads/projects';
            const projectPath = `${baseUrl}/${state.currentProjectId.value}/generated`;

            // 获取项目名称（从第一个视频的信息中提取并清理）
            const firstPair = state.generatedVideosList.value.find(pair =>
                state.selectedVideos.value.has(pair.video.filename)
            );
            const originalProjectName = firstPair ?
                (firstPair.video.originalVideoName || state.currentProjectId.value).replace(/\.[^/.]+$/, "") :
                state.currentProjectId.value;
            const projectName = cleanFileName(originalProjectName);

            let processedCount = 0;
            const totalFiles = state.selectedVideos.value.size * 2; // 视频 + 字幕

            // 用于存储已处理的文件名，防止重名
            const processedFileNames = new Set();

            // 更新进度消息
            const updateProgressMessage = (message) => {
                if (progressMessage) {
                    progressMessage.message = message;
                }
            };

            // 遍历选中的视频
            for (const filename of state.selectedVideos.value) {
                const pair = state.generatedVideosList.value.find(p => p.video.filename === filename);
                if (!pair) continue;

                try {
                    // 下载视频文件
                    console.log(`${functionLogPrefix} 正在下载视频: ${filename}`);

                    // 更新进度消息
                    updateProgressMessage(`正在下载文件 (${processedCount + 1}/${totalFiles})...`);

                    const videoUrl = `${projectPath}/${filename}`;
                    const videoResponse = await fetch(videoUrl);

                    if (videoResponse.ok) {
                        const videoArrayBuffer = await videoResponse.arrayBuffer();

                        // 清理文件名
                        const cleanedVideoName = cleanFileName(filename);
                        const videoExt = filename.split('.').pop();

                        // 处理重名文件
                        let finalVideoName = `${cleanedVideoName}.${videoExt}`;
                        let counter = 1;

                        while (processedFileNames.has(finalVideoName)) {
                            finalVideoName = `${cleanedVideoName}(${counter}).${videoExt}`;
                            counter++;
                        }

                        processedFileNames.add(finalVideoName);

                        // 添加到ZIP
                        zip.file(finalVideoName, videoArrayBuffer, {binary: true});
                        console.log(`${functionLogPrefix} 视频文件添加成功: ${finalVideoName} (${videoArrayBuffer.byteLength} bytes)`);
                    } else {
                        console.warn(`${functionLogPrefix} 视频文件下载失败: ${filename} - ${videoResponse.status}`);
                    }
                    processedCount++;

                    // 下载字幕文件（如果存在）
                    if (pair.subtitle && pair.subtitle.filename) {
                        console.log(`${functionLogPrefix} 正在下载字幕: ${pair.subtitle.filename}`);

                        // 更新进度消息
                        updateProgressMessage(`正在下载文件 (${processedCount + 1}/${totalFiles})...`);

                        const subtitleUrl = `${projectPath}/${pair.subtitle.filename}`;
                        const subtitleResponse = await fetch(subtitleUrl);

                        if (subtitleResponse.ok) {
                            const subtitleText = await subtitleResponse.text();
                            const subtitleExt = pair.subtitle.filename.split('.').pop();

                            // 清理字幕文件名
                            const cleanedVideoBaseName = cleanFileName(filename.replace(/\.[^/.]+$/, ""));

                            // 处理重名文件
                            let finalSubtitleName = `${cleanedVideoBaseName}_字幕.${subtitleExt}`;
                            let counter = 1;

                            while (processedFileNames.has(finalSubtitleName)) {
                                finalSubtitleName = `${cleanedVideoBaseName}_字幕(${counter}).${subtitleExt}`;
                                counter++;
                            }

                            processedFileNames.add(finalSubtitleName);

                            // 添加到ZIP
                            zip.file(finalSubtitleName, subtitleText, {binary: false});
                            console.log(`${functionLogPrefix} 字幕文件添加成功: ${finalSubtitleName} (${subtitleText.length} chars)`);
                        } else {
                            console.warn(`${functionLogPrefix} 字幕文件下载失败: ${pair.subtitle.filename} - ${subtitleResponse.status}`);
                        }
                    } else {
                        console.log(`${functionLogPrefix} 视频 ${filename} 无配对字幕`);
                    }
                    processedCount++;

                } catch (fileError) {
                    console.error(`${functionLogPrefix} 处理文件失败 ${filename}:`, fileError);
                    processedCount += 2; // 跳过视频和字幕
                }
            }

            // 生成ZIP文件
            console.log(`${functionLogPrefix} 正在生成ZIP文件...`);
            updateProgressMessage('正在打包文件，请稍候...');

            const zipBlob = await zip.generateAsync({
                type: 'blob',
                compression: 'DEFLATE',
                compressionOptions: {
                    level: 6
                },
                streamFiles: true
            });

            console.log(`${functionLogPrefix} ZIP文件生成成功，大小: ${zipBlob.size} bytes`);

            // 创建下载链接并触发下载
            const downloadUrl = URL.createObjectURL(zipBlob);
            const downloadLink = document.createElement('a');
            downloadLink.href = downloadUrl;
            downloadLink.download = `${projectName}_批量下载.zip`;
            downloadLink.style.display = 'none';

            // 添加到DOM，触发下载，然后移除
            document.body.appendChild(downloadLink);
            downloadLink.click();

            // 延迟清理，确保下载开始
            setTimeout(() => {
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(downloadUrl);
                console.log(`${functionLogPrefix} 下载链接已清理`);
            }, 100);

            // 关闭进度消息
            if (progressMessage) {
                progressMessage.close();
            }

            // 显示成功消息
            ElMessage.success(`✅ 成功下载 ${state.selectedVideos.value.size} 个视频文件到 ${projectName}_批量下载.zip`);
            console.log(`${functionLogPrefix} 批量下载完成`);

        } catch (error) {
            console.error(`${functionLogPrefix} 批量下载失败:`, error);

            // 关闭进度消息
            if (progressMessage) {
                progressMessage.close();
            }

            // 显示错误消息
            ElMessage.error(`❌ 批量下载失败: ${error.message}`);
        } finally {
            state.batchDownloading.value = false;
        }
    };

    /**
     * @功能概述: 生成直播视频草稿
     * @调用关系: 由"生成直播视频草稿"按钮调用
     * @状态影响: 更新generatingLiveVideoDraft状态，显示弹窗消息
     */
    const generateLiveVideoDraft = async () => {
        const functionLogPrefix = `${logPrefix}[generateLiveVideoDraft]`;

        if (state.selectedVideos.value.size === 0) {
            ElMessage.warning('请先选择要生成直播视频草稿的视频');
            return;
        }

        if (!state.currentProjectId.value) {
            ElMessage.error('项目ID不存在');
            return;
        }

        try {
            state.generatingLiveVideoDraft.value = true;

            console.log(`${functionLogPrefix} 开始生成直播视频草稿`);
            console.log(`${functionLogPrefix} 项目ID: ${state.currentProjectId.value}`);
            console.log(`${functionLogPrefix} 选中视频: ${Array.from(state.selectedVideos.value)}`);

            // 显示弹窗消息
            ElMessage({
                message: '生成直播视频草稿',
                type: 'info',
                duration: 3000
            });

            console.log(`${functionLogPrefix} 生成直播视频草稿功能已触发`);

        } catch (error) {
            console.error(`${functionLogPrefix} 生成直播视频草稿失败:`, error);
            ElMessage.error(`❌ 生成直播视频草稿失败: ${error.message}`);
        } finally {
            state.generatingLiveVideoDraft.value = false;
        }
    };

    /**
     * @功能概述: 切换全选状态
     * @调用关系: 由全选checkbox调用
     * @状态影响: 更新selectedVideos和selectAllVideos状态
     */
    const toggleSelectAll = (value) => {
        const functionLogPrefix = `${logPrefix}[toggleSelectAll]`;
        console.log(`${functionLogPrefix} 全选状态变更为: ${value}`);

        if (value) {
            // 执行全选
            const newSelectedVideos = new Set();
            state.generatedVideosList.value.forEach(pair => {
                newSelectedVideos.add(pair.video.filename);
            });
            state.selectedVideos.value = newSelectedVideos;
            console.log(`${functionLogPrefix} 全选所有视频，选中数量: ${state.selectedVideos.value.size}`);
        } else {
            // 执行取消全选
            state.selectedVideos.value = new Set();
            console.log(`${functionLogPrefix} 取消全选，清空选中列表`);
        }
    };

    return {
        initializeProjectListModule,  // 🆕 添加模板初始化函数
        loadProjectList,
        selectProject,
        resetProjectSelectionStates,  // 🆕 添加状态重置函数
        clearProjectSelection,
        confirmProjectSelection,
        renameProject,  // 🆕 添加项目重命名函数
        deleteProject,
        // 生成视频查看功能
        openGeneratedVideosDialog,
        closeGeneratedVideosDialog,
        toggleVideoSelection,
        toggleSelectAll,
        getVideoStatus,
        getVideoStatusText,
        getVideoStatusType,
        markSelectedAsPublished,
        batchDownloadSelected,
        generateLiveVideoDraft
    };
}

// ============================================================================
// 6. 视频生成模块
// ============================================================================

/**
 * @功能概述: 验证视频生成参数
 * @param {object} params - 待验证的参数对象
 * @returns {{isValid: boolean, message: string}} 验证结果
 */
function validateGenerateParams(params) {
    const logPrefix = '[文件：app.js][validateGenerateParams]'; // Changed from utils.js to app.js
    console.log(`${logPrefix} 正在验证生成参数:`, params);

    if (!params.script || params.script.trim() === '') {
        return { isValid: false, message: '视频文案不能为空' };
    }
    if (!params.videoIdentifier || params.videoIdentifier.trim() === '') {
        return { isValid: false, message: '视频ID不能为空' };
    }
    if (!params.voice || params.voice.trim() === '') {
        return { isValid: false, message: '必须选择一个配音' };
    }
    if (!params.videoStyle || params.videoStyle.trim() === '') {
        return { isValid: false, message: '必须选择一个视频风格' };
    }
    // ... 可根据需要添加更多验证规则

    console.log(`${logPrefix} 生成参数验证通过`);
    return { isValid: true, message: '验证通过' };
}

// ============================================================================
// 6.4 API通讯中断管理器
// ============================================================================

/**
 * @功能概述: SSE连接管理器
 * @用途: 管理EventSource连接，支持中断操作
 */
const sseConnectionManager = {
    currentConnection: null,
    connectionInfo: null,

    /**
     * @功能概述: 设置当前SSE连接
     * @参数: {EventSource} eventSource - SSE连接对象
     * @参数: {string} description - 连接描述
     */
    setConnection(eventSource, description = '未知连接') {
        const logPrefix = '[sseConnectionManager][setConnection]';

        // 如果已有连接，先关闭
        if (this.currentConnection) {
            console.log(`${logPrefix} 关闭现有连接: ${this.connectionInfo}`);
            this.currentConnection.close();
        }

        this.currentConnection = eventSource;
        this.connectionInfo = description;

        console.log(`${logPrefix} ✅ 设置新的SSE连接: ${description}`);
        console.log(`${logPrefix} 连接状态: ${eventSource.readyState === 0 ? '连接中' : eventSource.readyState === 1 ? '已连接' : '已关闭'}`);
    },

    /**
     * @功能概述: 中断当前SSE连接
     */
    abortConnection() {
        const logPrefix = '[sseConnectionManager][abortConnection]';

        if (this.currentConnection) {
            console.log(`${logPrefix} 🔌 开始中断SSE连接: ${this.connectionInfo}`);
            console.log(`${logPrefix} 中断前连接状态: ${this.currentConnection.readyState === 0 ? '连接中' : this.currentConnection.readyState === 1 ? '已连接' : '已关闭'}`);

            this.currentConnection.close();

            console.log(`${logPrefix} ✅ SSE连接已中断: ${this.connectionInfo}`);
            this.currentConnection = null;
            this.connectionInfo = null;
        } else {
            console.log(`${logPrefix} ⚠️ 没有活跃的SSE连接需要中断`);
        }
    },

    /**
     * @功能概述: 获取当前连接状态
     */
    getConnectionStatus() {
        if (!this.currentConnection) {
            return { connected: false, description: '无连接' };
        }

        return {
            connected: this.currentConnection.readyState === 1,
            readyState: this.currentConnection.readyState,
            description: this.connectionInfo
        };
    }
};

/**
 * @功能概述: HTTP请求中断控制器
 * @用途: 管理fetch请求，支持中断操作
 */
const requestAbortController = {
    currentController: null,
    requestInfo: null,

    /**
     * @功能概述: 创建新的中断控制器
     * @参数: {string} description - 请求描述
     * @返回值: {AbortSignal} 中断信号
     */
    createController(description = '未知请求') {
        const logPrefix = '[requestAbortController][createController]';

        // 如果已有控制器，先中断
        if (this.currentController) {
            console.log(`${logPrefix} 中断现有请求: ${this.requestInfo}`);
            this.currentController.abort();
        }

        this.currentController = new AbortController();
        this.requestInfo = description;

        console.log(`${logPrefix} ✅ 创建新的请求控制器: ${description}`);

        return this.currentController.signal;
    },

    /**
     * @功能概述: 中断当前HTTP请求
     */
    abortRequest() {
        const logPrefix = '[requestAbortController][abortRequest]';

        if (this.currentController) {
            console.log(`${logPrefix} 🚫 开始中断HTTP请求: ${this.requestInfo}`);

            this.currentController.abort();

            console.log(`${logPrefix} ✅ HTTP请求已中断: ${this.requestInfo}`);
            this.currentController = null;
            this.requestInfo = null;
        } else {
            console.log(`${logPrefix} ⚠️ 没有活跃的HTTP请求需要中断`);
        }
    },

    /**
     * @功能概述: 获取当前请求状态
     */
    getRequestStatus() {
        return {
            hasActiveRequest: !!this.currentController,
            description: this.requestInfo || '无请求'
        };
    }
};

// 将管理器挂载到全局，供其他模块使用
window.sseConnectionManager = sseConnectionManager;
window.requestAbortController = requestAbortController;

// ============================================================================
// 6.5 退出编辑功能
// ============================================================================

/**
 * @功能概述: 创建退出编辑功能
 * @参数说明: {object} state - 应用状态对象
 * @返回值: {object} 包含退出编辑相关函数的对象
 */
function createExitEditingFunctions(state) {
    const logPrefix = '[文件：app.js][createExitEditingFunctions]';

    /**
     * @功能概述: 显示退出编辑确认对话框
     * @返回值: {Promise<boolean>} 用户是否确认退出
     */
    const showExitConfirmation = async () => {
        const functionLogPrefix = `${logPrefix}[showExitConfirmation]`;

        try {
            console.log(`${functionLogPrefix} 显示退出编辑确认对话框`);

            const result = await ElementPlus.ElMessageBox.confirm(
                '退出编辑将丢失所有未保存的编辑内容，确定要继续吗？\n\n包括：\n• 已选择的视频片段\n• 画面裁剪设置\n• 参数配置',
                '确认退出编辑',
                {
                    confirmButtonText: '确定退出',
                    cancelButtonText: '继续编辑',
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                    distinguishCancelAndClose: true
                }
            );

            console.log(`${functionLogPrefix} 用户确认退出编辑`);
            return result === 'confirm';

        } catch (error) {
            if (error === 'cancel' || error === 'close') {
                console.log(`${functionLogPrefix} 用户取消退出编辑`);
                return false;
            }

            console.error(`${functionLogPrefix} 显示确认对话框失败:`, error);
            return false;
        }
    };

    /**
     * @功能概述: 显示生成模式专用的退出确认对话框
     * @返回值: {Promise<boolean>} 用户是否确认退出
     */
    const showGenerationExitConfirmation = async () => {
        const functionLogPrefix = `${logPrefix}[showGenerationExitConfirmation]`;

        try {
            console.log(`${functionLogPrefix} 显示生成模式退出确认对话框`);

            // 获取当前通讯状态
            const sseStatus = window.sseConnectionManager.getConnectionStatus();
            const requestStatus = window.requestAbortController.getRequestStatus();

            console.log(`${functionLogPrefix} 当前通讯状态:`);
            console.log(`${functionLogPrefix}   - SSE连接: ${JSON.stringify(sseStatus)}`);
            console.log(`${functionLogPrefix}   - HTTP请求: ${JSON.stringify(requestStatus)}`);

            const result = await ElementPlus.ElMessageBox.confirm(
                '检测到正在生成视频，退出编辑将中断生成过程。\n\n将会中断：\n• 正在进行的视频生成\n• 与服务器的通讯连接\n• 所有编辑状态和配置\n\n确定要退出吗？',
                '确认中断生成并退出编辑',
                {
                    confirmButtonText: '确定退出',
                    cancelButtonText: '继续生成',
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                    distinguishCancelAndClose: true
                }
            );

            console.log(`${functionLogPrefix} 用户确认中断生成并退出编辑`);
            return result === 'confirm';

        } catch (error) {
            if (error === 'cancel' || error === 'close') {
                console.log(`${functionLogPrefix} 用户取消退出编辑，继续生成`);
                return false;
            }

            console.error(`${functionLogPrefix} 显示生成模式确认对话框失败:`, error);
            return false;
        }
    };

    /**
     * @功能概述: 动态监控所有编辑器状态
     */
    const logAllEditorStates = (stage) => {
        const functionLogPrefix = `${logPrefix}[logAllEditorStates][${stage}]`;

        try {
            console.log(`${functionLogPrefix} ==================== 编辑器状态监控 ====================`);

            if (window.videoEditorStateGlobal) {
                const videoEditorState = window.videoEditorStateGlobal;

                console.log(`${functionLogPrefix} 🎬 视频播放器状态:`);
                console.log(`  - videoEditorPlayer:`, videoEditorState.videoEditorPlayer?.value);
                console.log(`  - videoCurrentTime:`, videoEditorState.videoCurrentTime?.value);
                console.log(`  - videoDurationRef:`, videoEditorState.videoDurationRef?.value);
                console.log(`  - playbackSpeed:`, videoEditorState.playbackSpeed?.value);

                console.log(`${functionLogPrefix} 📝 编辑器模式状态:`);
                console.log(`  - editorMode:`, videoEditorState.editorMode?.value);

                console.log(`${functionLogPrefix} ✂️ 片段选择状态:`);
                console.log(`  - clipSegments:`, videoEditorState.clipSegments?.value);
                console.log(`  - currentSegmentIndex:`, videoEditorState.currentSegmentIndex?.value);
                console.log(`  - isSelectingStart:`, videoEditorState.isSelectingStart?.value);

                console.log(`${functionLogPrefix} 🖼️ 裁剪功能状态:`);
                console.log(`  - cropDataForBackend:`, videoEditorState.cropDataForBackend?.value);
                console.log(`  - confirmedCropParams:`, videoEditorState.confirmedCropParams?.value);
                console.log(`  - cropperInstance:`, videoEditorState.cropperInstance?.value);

                console.log(`${functionLogPrefix} 🔄 预览功能状态:`);
                console.log(`  - previewTimer:`, videoEditorState.previewTimer?.value);
                console.log(`  - canvasUpdateTimer:`, videoEditorState.canvasUpdateTimer?.value);
                console.log(`  - canvasImageTimer:`, videoEditorState.canvasImageTimer?.value);
                console.log(`  - previewIndex:`, videoEditorState.previewIndex?.value);
                console.log(`  - previewTimePoints:`, videoEditorState.previewTimePoints?.value);
                console.log(`  - imageCollection:`, videoEditorState.imageCollection?.value);
                console.log(`  - isCollectingImages:`, videoEditorState.isCollectingImages?.value);
                console.log(`  - isImageCollectionReady:`, videoEditorState.isImageCollectionReady?.value);

                console.log(`${functionLogPrefix} 📄 字幕显示状态:`);
                console.log(`  - englishSubtitles:`, videoEditorState.englishSubtitles?.value);
                console.log(`  - currentSubtitles:`, videoEditorState.currentSubtitles?.value);

                console.log(`${functionLogPrefix} 🎛️ 其他可能的状态:`);
                // 遍历所有可能的状态
                Object.keys(videoEditorState).forEach(key => {
                    if (!['videoEditorPlayer', 'videoCurrentTime', 'videoDurationRef', 'playbackSpeed',
                          'editorMode', 'clipSegments', 'currentSegmentIndex', 'isSelectingStart',
                          'cropDataForBackend', 'confirmedCropParams', 'cropperInstance',
                          'previewTimer', 'canvasUpdateTimer', 'canvasImageTimer', 'previewIndex',
                          'previewTimePoints', 'imageCollection', 'isCollectingImages', 'isImageCollectionReady',
                          'englishSubtitles', 'currentSubtitles'].includes(key)) {
                        console.log(`  - ${key}:`, videoEditorState[key]?.value || videoEditorState[key]);
                    }
                });
            } else {
                console.log(`${functionLogPrefix} ❌ 没有找到window.videoEditorStateGlobal`);
            }

            console.log(`${functionLogPrefix} ==================== 状态监控结束 ====================`);

        } catch (error) {
            console.error(`${functionLogPrefix} 状态监控失败:`, error);
        }
    };

    /**
     * @功能概述: 重置视频播放器状态
     */
    const resetVideoPlayerStates = () => {
        const functionLogPrefix = `${logPrefix}[resetVideoPlayerStates]`;

        try {
            console.log(`${functionLogPrefix} ==================== 开始重置视频播放器状态 ====================`);

            // 检查是否有视频编辑器状态
            if (window.videoEditorStateGlobal) {
                const videoEditorState = window.videoEditorStateGlobal;

                // 重置视频播放器相关状态
                if (videoEditorState.videoEditorPlayer && videoEditorState.videoEditorPlayer.value) {
                    const player = videoEditorState.videoEditorPlayer.value;
                    console.log(`${functionLogPrefix} 🎬 重置前播放器状态:`);
                    console.log(`${functionLogPrefix}   - 播放状态: ${player.paused ? '暂停' : '播放'}`);
                    console.log(`${functionLogPrefix}   - 当前时间: ${player.currentTime}秒`);
                    console.log(`${functionLogPrefix}   - 播放速度: ${player.playbackRate}x`);

                    player.pause();
                    player.currentTime = 0;
                    player.playbackRate = 1;

                    console.log(`${functionLogPrefix} 🎬 重置后播放器状态:`);
                    console.log(`${functionLogPrefix}   - 播放状态: ${player.paused ? '暂停' : '播放'}`);
                    console.log(`${functionLogPrefix}   - 当前时间: ${player.currentTime}秒`);
                    console.log(`${functionLogPrefix}   - 播放速度: ${player.playbackRate}x`);
                } else {
                    console.log(`${functionLogPrefix} ⚠️ 视频播放器元素不存在或为null`);
                }

                // 重置播放相关状态
                if (videoEditorState.videoCurrentTime) {
                    console.log(`${functionLogPrefix} 📊 重置前 videoCurrentTime: ${videoEditorState.videoCurrentTime.value}秒`);
                    videoEditorState.videoCurrentTime.value = 0;
                    console.log(`${functionLogPrefix} 📊 重置后 videoCurrentTime: ${videoEditorState.videoCurrentTime.value}秒`);
                }

                if (videoEditorState.videoDurationRef) {
                    console.log(`${functionLogPrefix} 📊 重置前 videoDurationRef: ${videoEditorState.videoDurationRef.value}秒`);
                    videoEditorState.videoDurationRef.value = 0;
                    console.log(`${functionLogPrefix} 📊 重置后 videoDurationRef: ${videoEditorState.videoDurationRef.value}秒`);
                }

                if (videoEditorState.playbackSpeed) {
                    console.log(`${functionLogPrefix} 📊 重置前 playbackSpeed: ${videoEditorState.playbackSpeed.value}x`);
                    videoEditorState.playbackSpeed.value = 1;
                    console.log(`${functionLogPrefix} 📊 重置后 playbackSpeed: ${videoEditorState.playbackSpeed.value}x`);
                }

                console.log(`${functionLogPrefix} ✅ 视频播放器状态重置完成`);
            } else {
                console.log(`${functionLogPrefix} ❌ 无法找到videoEditorStateGlobal，跳过视频播放器重置`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} ❌ 重置视频播放器状态失败:`, error);
        }
    };

    /**
     * @功能概述: 重置片段选择状态
     */
    const resetSegmentStates = () => {
        const functionLogPrefix = `${logPrefix}[resetSegmentStates]`;

        try {
            console.log(`${functionLogPrefix} ==================== 开始重置片段选择状态 ====================`);

            if (window.videoEditorStateGlobal) {
                const videoEditorState = window.videoEditorStateGlobal;

                // 重置片段选择相关状态
                if (videoEditorState.clipSegments) {
                    console.log(`${functionLogPrefix} ✂️ 重置前 clipSegments:`);
                    console.log(`${functionLogPrefix}   - 片段数量: ${videoEditorState.clipSegments.value.length}`);
                    videoEditorState.clipSegments.value.forEach((segment, index) => {
                        console.log(`${functionLogPrefix}   - 片段${index + 1}: 开始=${segment.startTime}秒, 结束=${segment.endTime}秒`);
                    });

                    videoEditorState.clipSegments.value = [];

                    console.log(`${functionLogPrefix} ✂️ 重置后 clipSegments:`);
                    console.log(`${functionLogPrefix}   - 片段数量: ${videoEditorState.clipSegments.value.length}`);
                }

                if (videoEditorState.currentSegmentIndex) {
                    console.log(`${functionLogPrefix} 📍 重置前 currentSegmentIndex: ${videoEditorState.currentSegmentIndex.value}`);
                    videoEditorState.currentSegmentIndex.value = 0;
                    console.log(`${functionLogPrefix} 📍 重置后 currentSegmentIndex: ${videoEditorState.currentSegmentIndex.value}`);
                }

                if (videoEditorState.isSelectingStart) {
                    console.log(`${functionLogPrefix} 🎯 重置前 isSelectingStart: ${videoEditorState.isSelectingStart.value ? '选择开始时间' : '选择结束时间'}`);
                    videoEditorState.isSelectingStart.value = true;
                    console.log(`${functionLogPrefix} 🎯 重置后 isSelectingStart: ${videoEditorState.isSelectingStart.value ? '选择开始时间' : '选择结束时间'}`);
                }

                // 检查计算属性状态
                if (videoEditorState.clipStartTime) {
                    console.log(`${functionLogPrefix} 🔗 计算属性 clipStartTime: ${videoEditorState.clipStartTime.value}`);
                }
                if (videoEditorState.clipEndTime) {
                    console.log(`${functionLogPrefix} 🔗 计算属性 clipEndTime: ${videoEditorState.clipEndTime.value}`);
                }

                console.log(`${functionLogPrefix} ✅ 片段选择状态重置完成`);
            } else {
                console.log(`${functionLogPrefix} ❌ 无法找到videoEditorStateGlobal，跳过片段选择重置`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} ❌ 重置片段选择状态失败:`, error);
        }
    };

    /**
     * @功能概述: 重置裁剪功能状态
     */
    const resetCroppingStates = () => {
        const functionLogPrefix = `${logPrefix}[resetCroppingStates]`;

        try {
            console.log(`${functionLogPrefix} 开始重置裁剪功能状态`);

            if (window.videoEditorStateGlobal) {
                const videoEditorState = window.videoEditorStateGlobal;

                // 销毁Cropper.js实例
                if (videoEditorState.cropperInstance && videoEditorState.cropperInstance.value) {
                    videoEditorState.cropperInstance.value.destroy();
                    videoEditorState.cropperInstance.value = null;
                    console.log(`${functionLogPrefix} Cropper.js实例已销毁`);
                }

                // 重置裁剪相关状态
                if (videoEditorState.cropDataForBackend) {
                    videoEditorState.cropDataForBackend.value = {
                        isCroppingActive: false,
                        cropWidth: null,
                        cropHeight: null,
                        cropXOffset: null,
                        cropYOffset: null
                    };
                }

                if (videoEditorState.confirmedCropParams) {
                    videoEditorState.confirmedCropParams.value = {
                        isConfirmed: false,
                        x: null,
                        y: null,
                        width: null,
                        height: null
                    };
                }

                console.log(`${functionLogPrefix} 裁剪功能状态重置完成`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} 重置裁剪功能状态失败:`, error);
        }
    };

    /**
     * @功能概述: 重置预览功能状态
     */
    const resetPreviewStates = () => {
        const functionLogPrefix = `${logPrefix}[resetPreviewStates]`;

        try {
            console.log(`${functionLogPrefix} 开始重置预览功能状态`);

            if (window.videoEditorStateGlobal) {
                const videoEditorState = window.videoEditorStateGlobal;

                // 清除所有定时器
                if (videoEditorState.previewTimer && videoEditorState.previewTimer.value) {
                    clearInterval(videoEditorState.previewTimer.value);
                    videoEditorState.previewTimer.value = null;
                }

                if (videoEditorState.canvasUpdateTimer && videoEditorState.canvasUpdateTimer.value) {
                    clearInterval(videoEditorState.canvasUpdateTimer.value);
                    videoEditorState.canvasUpdateTimer.value = null;
                }

                if (videoEditorState.canvasImageTimer && videoEditorState.canvasImageTimer.value) {
                    clearInterval(videoEditorState.canvasImageTimer.value);
                    videoEditorState.canvasImageTimer.value = null;
                }

                // 重置预览相关状态
                if (videoEditorState.previewIndex) {
                    videoEditorState.previewIndex.value = 0;
                }
                if (videoEditorState.previewTimePoints) {
                    videoEditorState.previewTimePoints.value = [];
                }
                if (videoEditorState.imageCollection) {
                    videoEditorState.imageCollection.value = [];
                }
                if (videoEditorState.isCollectingImages) {
                    videoEditorState.isCollectingImages.value = false;
                }
                if (videoEditorState.isImageCollectionReady) {
                    videoEditorState.isImageCollectionReady.value = false;
                }

                console.log(`${functionLogPrefix} 预览功能状态重置完成`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} 重置预览功能状态失败:`, error);
        }
    };

    /**
     * @功能概述: 重置参数设置状态 - 这是最重要的重置功能
     */
    const resetParameterSettingsStates = () => {
        const functionLogPrefix = `${logPrefix}[resetParameterSettingsStates]`;

        try {
            console.log(`${functionLogPrefix} ==================== 开始重置参数设置状态 ====================`);

            // 重置videoConfig状态 - 这是参数设置的核心
            if (state.videoConfig && state.videoConfig.value) {
                const currentConfig = state.videoConfig.value;

                console.log(`${functionLogPrefix} 🎛️ 重置前 videoConfig 状态:`);
                console.log(`${functionLogPrefix}   - 重复次数: ${currentConfig.repeatCount}`);
                console.log(`${functionLogPrefix}   - 背景风格: ${currentConfig.backgroundStyle}`);
                console.log(`${functionLogPrefix}   - 重复模式数量: ${currentConfig.repeatModes ? currentConfig.repeatModes.length : 0}`);
                if (currentConfig.repeatModes) {
                    currentConfig.repeatModes.forEach((mode, index) => {
                        console.log(`${functionLogPrefix}     模式${index + 1}: ${mode.name} - ${mode.displayText}`);
                    });
                }
                console.log(`${functionLogPrefix}   - 视频指导启用: ${currentConfig.subtitleConfig?.videoGuide?.enabled}`);
                console.log(`${functionLogPrefix}   - 视频指导标题1: "${currentConfig.subtitleConfig?.videoGuide?.title1}"`);
                console.log(`${functionLogPrefix}   - 视频指导标题2: "${currentConfig.subtitleConfig?.videoGuide?.title2}"`);
                console.log(`${functionLogPrefix}   - 广告启用: ${currentConfig.subtitleConfig?.advertisement?.enabled}`);
                console.log(`${functionLogPrefix}   - 广告标题数量: ${currentConfig.subtitleConfig?.advertisement?.titles?.length || 0}`);

                // 重置为默认配置
                const defaultConfig = {
                    repeatCount: 3,
                    repeatModes: RepeatModeManager.getDefaultModeSequence(3),
                    backgroundStyle: "newspaper",
                    subtitleConfig: {
                        videoGuide: {
                            enabled: true,
                            title1: "坚持30天",
                            title2: "听懂国外新闻"
                        },
                        advertisement: {
                            enabled: true,
                            titles: [
                                {
                                    line1: "🌍关注水蜜桃英语，",
                                    line2: "每天2分钟，听全球要闻！"
                                },
                                {
                                    line1: "🌍关注水蜜桃英语，",
                                    line2: "每天2分钟，听力大提升！"
                                },
                                {
                                    line1: "🌍关注水蜜桃英语，",
                                    line2: "不靠字幕，听懂世界声！"
                                }
                            ]
                        }
                    }
                };

                // 深度重置配置
                state.videoConfig.value = JSON.parse(JSON.stringify(defaultConfig));

                console.log(`${functionLogPrefix} 🎛️ 重置后 videoConfig 状态:`);
                console.log(`${functionLogPrefix}   - 重复次数: ${state.videoConfig.value.repeatCount}`);
                console.log(`${functionLogPrefix}   - 背景风格: ${state.videoConfig.value.backgroundStyle}`);
                console.log(`${functionLogPrefix}   - 重复模式数量: ${state.videoConfig.value.repeatModes.length}`);
                state.videoConfig.value.repeatModes.forEach((mode, index) => {
                    console.log(`${functionLogPrefix}     模式${index + 1}: ${mode.name} - ${mode.displayText}`);
                });
                console.log(`${functionLogPrefix}   - 视频指导启用: ${state.videoConfig.value.subtitleConfig.videoGuide.enabled}`);
                console.log(`${functionLogPrefix}   - 视频指导标题1: "${state.videoConfig.value.subtitleConfig.videoGuide.title1}"`);
                console.log(`${functionLogPrefix}   - 视频指导标题2: "${state.videoConfig.value.subtitleConfig.videoGuide.title2}"`);
                console.log(`${functionLogPrefix}   - 广告启用: ${state.videoConfig.value.subtitleConfig.advertisement.enabled}`);
                console.log(`${functionLogPrefix}   - 广告标题数量: ${state.videoConfig.value.subtitleConfig.advertisement.titles.length}`);

                console.log(`${functionLogPrefix} ✅ 参数设置状态重置完成`);
            } else {
                console.log(`${functionLogPrefix} ❌ 无法找到videoConfig状态，跳过参数设置重置`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} ❌ 重置参数设置状态失败:`, error);
        }
    };

    /**
     * @功能概述: 重置字幕显示状态
     */
    const resetSubtitleStates = () => {
        const functionLogPrefix = `${logPrefix}[resetSubtitleStates]`;

        try {
            console.log(`${functionLogPrefix} ==================== 开始重置字幕显示状态 ====================`);

            if (window.videoEditorStateGlobal) {
                const videoEditorState = window.videoEditorStateGlobal;

                // 重置字幕相关状态
                if (videoEditorState.englishSubtitles) {
                    console.log(`${functionLogPrefix} 📄 重置前 englishSubtitles 数量: ${videoEditorState.englishSubtitles.value.length}`);
                    videoEditorState.englishSubtitles.value.forEach((subtitle, index) => {
                        console.log(`${functionLogPrefix}   字幕${index + 1}: ${subtitle.startTime}s-${subtitle.endTime}s "${subtitle.text}"`);
                    });

                    videoEditorState.englishSubtitles.value = [];
                    console.log(`${functionLogPrefix} 📄 重置后 englishSubtitles 数量: ${videoEditorState.englishSubtitles.value.length}`);
                }

                if (videoEditorState.currentSubtitles) {
                    console.log(`${functionLogPrefix} 📝 重置前 currentSubtitles: "${videoEditorState.currentSubtitles.value}"`);
                    videoEditorState.currentSubtitles.value = '';
                    console.log(`${functionLogPrefix} 📝 重置后 currentSubtitles: "${videoEditorState.currentSubtitles.value}"`);
                }

                console.log(`${functionLogPrefix} ✅ 字幕显示状态重置完成`);
            } else {
                console.log(`${functionLogPrefix} ❌ 无法找到videoEditorStateGlobal，跳过字幕显示重置`);
            }

        } catch (error) {
            console.error(`${functionLogPrefix} ❌ 重置字幕显示状态失败:`, error);
        }
    };

    /**
     * @功能概述: 清理DOM元素
     */
    const cleanupDOMElements = () => {
        const functionLogPrefix = `${logPrefix}[cleanupDOMElements]`;

        try {
            console.log(`${functionLogPrefix} 开始清理DOM元素`);

            // 清理Canvas元素
            const canvasElements = ['cropperCanvas', 'frameCroppingCanvas'];
            canvasElements.forEach(canvasId => {
                const canvas = document.getElementById(canvasId);
                if (canvas && canvas.isConnected) {
                    canvas.remove();
                    console.log(`${functionLogPrefix} 移除Canvas元素: ${canvasId}`);
                }
            });

            // 清理裁剪框元素
            const cropElements = ['customCropBox', 'cropSelection'];
            cropElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element && element.isConnected) {
                    element.remove();
                    console.log(`${functionLogPrefix} 移除裁剪元素: ${elementId}`);
                }
            });

            // 清理所有cropper相关的元素
            const cropperContainers = document.querySelectorAll('.cropper-container');
            cropperContainers.forEach(container => {
                if (container && container.isConnected) {
                    container.remove();
                    console.log(`${functionLogPrefix} 移除Cropper容器`);
                }
            });

            // 🆕 清理项目列表模块容器（如果存在）
            const projectListContainer = document.getElementById('project-list-container');
            if (projectListContainer && projectListContainer.isConnected) {
                // 只清理内容，不移除容器本身
                projectListContainer.innerHTML = `
                    <!-- 项目列表模块内容将通过ProjectListModule动态加载到此容器中 -->
                    <!-- 如果模块加载失败，显示降级内容 -->
                    <div class="module-loading-fallback">
                        <div class="content-section">
                            <h3>选择已有视频</h3>
                            <p>正在初始化项目列表模块...</p>
                        </div>
                    </div>
                `;
                console.log(`${functionLogPrefix} 重置项目列表容器内容`);
            }

            console.log(`${functionLogPrefix} DOM元素清理完成`);

        } catch (error) {
            console.error(`${functionLogPrefix} 清理DOM元素失败:`, error);
        }
    };

    /**
     * @功能概述: 重置主应用状态
     */
    const resetMainAppStates = () => {
        const functionLogPrefix = `${logPrefix}[resetMainAppStates]`;

        try {
            console.log(`${functionLogPrefix} 开始重置主应用状态`);

            // 重置界面状态
            state.activeSection.value = 'upload';
            state.selectedProject.value = null;
            state.editorData.value = {};

            // 🆕 重置项目选择对话框状态
            state.showProjectSelectionDialog.value = false;
            state.dialogSelectedProject.value = null;

            // 重置编辑器模式
            if (window.videoEditorStateGlobal) {
                const videoEditorState = window.videoEditorStateGlobal;
                if (videoEditorState.editorMode) {
                    videoEditorState.editorMode.value = 'segment-clipping';
                }
            }

            // 🆕 设置标志阻止重新初始化
            window.isExitingEditMode = true;

            // 延迟清除标志，确保不会在退出过程中重新初始化
            setTimeout(() => {
                window.isExitingEditMode = false;
            }, 1000);

            console.log(`${functionLogPrefix} 主应用状态重置完成`);

        } catch (error) {
            console.error(`${functionLogPrefix} 重置主应用状态失败:`, error);
        }
    };

    /**
     * @功能概述: 重置生成状态和中断通讯
     */
    const resetGenerationStates = () => {
        const functionLogPrefix = `${logPrefix}[resetGenerationStates]`;

        try {
            console.log(`${functionLogPrefix} ==================== 开始重置生成状态和中断通讯 ====================`);

            // 1. 中断SSE连接
            console.log(`${functionLogPrefix} 🔌 步骤1: 中断SSE连接`);
            const sseStatus = window.sseConnectionManager.getConnectionStatus();
            console.log(`${functionLogPrefix} SSE连接状态: ${JSON.stringify(sseStatus)}`);
            window.sseConnectionManager.abortConnection();

            // 2. 中断HTTP请求
            console.log(`${functionLogPrefix} 🚫 步骤2: 中断HTTP请求`);
            const requestStatus = window.requestAbortController.getRequestStatus();
            console.log(`${functionLogPrefix} HTTP请求状态: ${JSON.stringify(requestStatus)}`);
            window.requestAbortController.abortRequest();

            // 3. 重置生成状态
            console.log(`${functionLogPrefix} 📊 步骤3: 重置生成相关状态`);

            if (state.isGenerating) {
                console.log(`${functionLogPrefix} 重置前 isGenerating: ${state.isGenerating.value}`);
                state.isGenerating.value = false;
                console.log(`${functionLogPrefix} 重置后 isGenerating: ${state.isGenerating.value}`);
            }

            if (state.isGenerationCompleted) {
                console.log(`${functionLogPrefix} 重置前 isGenerationCompleted: ${state.isGenerationCompleted.value}`);
                state.isGenerationCompleted.value = false;
                console.log(`${functionLogPrefix} 重置后 isGenerationCompleted: ${state.isGenerationCompleted.value}`);
            }

            if (state.isGenerationFailed) {
                console.log(`${functionLogPrefix} 重置前 isGenerationFailed: ${state.isGenerationFailed.value}`);
                state.isGenerationFailed.value = false;
                console.log(`${functionLogPrefix} 重置后 isGenerationFailed: ${state.isGenerationFailed.value}`);
            }

            // 4. 清空状态消息
            console.log(`${functionLogPrefix} 📝 步骤4: 清空状态消息`);
            if (state.generationStatusMessages) {
                console.log(`${functionLogPrefix} 重置前 generationStatusMessages 数量: ${state.generationStatusMessages.value.length}`);
                state.generationStatusMessages.value.forEach((msg, index) => {
                    console.log(`${functionLogPrefix}   消息${index + 1}: ${msg.type} - ${msg.message}`);
                });
                state.generationStatusMessages.value = [];
                console.log(`${functionLogPrefix} 重置后 generationStatusMessages 数量: ${state.generationStatusMessages.value.length}`);
            }

            if (state.uploadStatusMessages) {
                console.log(`${functionLogPrefix} 重置前 uploadStatusMessages 数量: ${state.uploadStatusMessages.value.length}`);
                state.uploadStatusMessages.value = [];
                console.log(`${functionLogPrefix} 重置后 uploadStatusMessages 数量: ${state.uploadStatusMessages.value.length}`);
            }

            // 5. 清空下载链接
            console.log(`${functionLogPrefix} 🔗 步骤5: 清空下载链接`);
            if (state.downloadLinks) {
                console.log(`${functionLogPrefix} 重置前 downloadLinks:`, {
                    enhancedBilingualSubtitle: state.downloadLinks.value.enhancedBilingualSubtitle,
                    finalVideo: state.downloadLinks.value.finalVideo
                });
                state.downloadLinks.value = {
                    enhancedBilingualSubtitle: null,
                    finalVideo: null
                };
                console.log(`${functionLogPrefix} 重置后 downloadLinks:`, {
                    enhancedBilingualSubtitle: state.downloadLinks.value.enhancedBilingualSubtitle,
                    finalVideo: state.downloadLinks.value.finalVideo
                });
            }

            // 6. 重置SSE状态
            console.log(`${functionLogPrefix} 📡 步骤6: 重置SSE状态`);
            if (state.sseStatus) {
                console.log(`${functionLogPrefix} 重置前 sseStatus:`, state.sseStatus.value);
                // 🆕 重置为正确的对象结构，而不是字符串
                state.sseStatus.value = {
                    connection: {
                        isConnected: false,
                        connectionId: null,
                        startTime: null,
                        lastHeartbeat: null
                    },
                    pipeline: {
                        name: null,
                        status: null,
                        progress: 0,
                        currentTask: null,
                        totalTasks: 0,
                        completedTasks: 0
                    }
                };
                console.log(`${functionLogPrefix} 重置后 sseStatus:`, state.sseStatus.value);
            }

            console.log(`${functionLogPrefix} ✅ 生成状态和通讯重置完成`);

        } catch (error) {
            console.error(`${functionLogPrefix} ❌ 重置生成状态失败:`, error);
        }
    };

    return {
        logAllEditorStates, // 🆕 状态监控函数
        showExitConfirmation,
        showGenerationExitConfirmation, // 🆕 生成模式确认对话框
        resetVideoPlayerStates,
        resetSegmentStates,
        resetCroppingStates,
        resetPreviewStates,
        resetParameterSettingsStates, // 🆕 参数设置重置函数
        resetSubtitleStates,
        resetGenerationStates, // 🆕 生成状态重置函数
        cleanupDOMElements,
        resetMainAppStates
    };
}

// ============================================================================
// 7. Vue应用创建和配置
// ============================================================================

/**
 * @功能概述: 创建并配置Vue应用实例
 * @返回值: {object} 配置完成的Vue应用实例
 */
function createVueApp() {
    const logPrefix = '[文件：app.js][createVueApp]';
    console.log(`${logPrefix} createVueApp函数被调用`);

    const app = createApp({
        setup() {
            console.log(`${logPrefix} Vue setup函数开始执行`);

            // 步骤 1: 创建和解构所有状态和计算属性
            const state = createAppState();
            const {
                activeSection,
                isUploading,
                selectedFile,
                isExitingEdit, // 🆕 退出编辑中状态

                // 项目选择状态
                projectList,
                selectedProject,
                isLoadingProjects,
                projectLoadError,
                projectPagination,
                showProjectSelectionDialog,  // 🆕 项目选择对话框显示状态
                dialogSelectedProject,       // 🆕 对话框中选中的项目

                editorData,
                uploadStatusMessages,
                generationStatusMessages,
                isGenerating,
                isGenerationCompleted,
                isGenerationFailed,
                downloadLinks,
                videoConfig,
                sseStatus,

                // 生成视频查看对话框状态
                showGeneratedVideosDialog,
                generatedVideosList,
                selectedVideos,
                selectAllVideos,
                currentProjectId,
                loadingGeneratedVideos,
                videoStatusMap,
                markingAsPublished,
                batchDownloading,
                generatingLiveVideoDraft
            } = state;

            const videoEditorState = window.VideoEditor.createVideoEditorState();
            const videoEditorComputed = window.VideoEditor.createVideoEditorComputed(videoEditorState);

            // 设置全局引用以便其他地方访问
            window.videoEditorStateGlobal = videoEditorState;

            const computedProps = createComputedProperties(state, videoEditorState);
            const {
                editingIndicatorText,
                isParameterSettingsEnabled,
                isGenerationEnabled,
                getCurrentTime,
            } = computedProps;

            // 🆕 退出编辑按钮状态控制
            const exitButtonState = computed(() => {
                const functionLogPrefix = '[文件：app.js][exitButtonState]';

                try {
                    // 画面裁剪模式下的特殊处理 - 只要进入裁剪模式就禁用退出按钮
                    if (videoEditorState.editorMode.value === 'frame-cropping') {
                        console.log(`${functionLogPrefix} 🖼️ 检测到画面裁剪模式，禁用退出编辑按钮`);

                        return {
                            disabled: true, // 画面裁剪模式下禁用退出按钮
                            tooltip: '画面裁剪模式下无法退出编辑，请先切换到其他模式',
                            buttonText: '退出编辑 (裁剪中)',
                            type: 'warning'
                        };
                    }

                    // 生成过程中允许退出（用于中断通讯）
                    if (state.isGenerating.value) {
                        console.log(`${functionLogPrefix} ⚡ 生成模式下允许退出（中断通讯）`);
                        return {
                            disabled: false,
                            tooltip: '点击退出将中断当前生成过程',
                            buttonText: '中断生成并退出',
                            type: 'danger'
                        };
                    }

                    // 默认状态
                    console.log(`${functionLogPrefix} ✅ 正常模式下允许退出`);
                    return {
                        disabled: false,
                        tooltip: '退出编辑模式，返回上传界面',
                        buttonText: '退出编辑',
                        type: 'danger'
                    };

                } catch (error) {
                    console.error(`${functionLogPrefix} ❌ 计算退出按钮状态失败:`, error);
                    return {
                        disabled: false,
                        tooltip: '退出编辑模式',
                        buttonText: '退出编辑',
                        type: 'danger'
                    };
                }
            });

            // 步骤 2: 创建和解构所有功能函数
            const uploadFunctions = createUploadFunctions(state);
            const { triggerFileInput, uploadVideo } = uploadFunctions;
            
            const navigationFunctions = createNavigationFunctions(state);
            const { navigateTo } = navigationFunctions;

            const projectManagementFunctions = createProjectManagementFunctions(state);
            const {
                initializeProjectListModule,  // 🆕 添加模板初始化函数
                loadProjectList,
                selectProject,
                resetProjectSelectionStates,  // 🆕 添加状态重置函数
                clearProjectSelection,
                confirmProjectSelection,
                renameProject,  // 🆕 添加项目重命名函数
                deleteProject,
                // 生成视频查看功能
                openGeneratedVideosDialog,
                closeGeneratedVideosDialog,
                toggleVideoSelection,
                toggleSelectAll,
                getVideoStatus,
                getVideoStatusText,
                getVideoStatusType,
                markSelectedAsPublished,
                batchDownloadSelected,
                generateLiveVideoDraft
            } = projectManagementFunctions;

            // 🆕 退出编辑功能
            const exitEditingFunctions = createExitEditingFunctions(state);
            const {
                logAllEditorStates, // 🆕 状态监控函数
                showExitConfirmation,
                showGenerationExitConfirmation, // 🆕 生成模式确认对话框
                resetVideoPlayerStates,
                resetSegmentStates,
                resetCroppingStates,
                resetPreviewStates,
                resetParameterSettingsStates, // 🆕 参数设置重置函数
                resetSubtitleStates,
                resetGenerationStates, // 🆕 生成状态重置函数
                cleanupDOMElements,
                resetMainAppStates
            } = exitEditingFunctions;

            const videoPlaybackFunctions = window.VideoEditor.createVideoPlaybackFunctions(videoEditorState);
            const segmentFunctions = window.VideoEditor.createSegmentFunctions(videoEditorState);
            const modeSwitchFunctions = window.VideoEditor.createModeSwitchFunctions(videoEditorState, segmentFunctions);
            const croppingFunctions = window.VideoEditor.createCroppingFunctions(videoEditorState, segmentFunctions);
            
            // 解构出需要单独使用的函数
            const { navigateToSegmentClipping, navigateToFrameCropping, navigateToParameterSettings, navigateToGeneration } = modeSwitchFunctions;

            // 步骤 3: ref引用
            const fileInput = ref(null);
            const videoEditorPlayer = ref(null);
            
            // 将ref与功能模块关联
            videoPlaybackFunctions.setVideoPlayerRef(videoEditorPlayer);
            segmentFunctions.setVideoPlayerRef(videoEditorPlayer);
            modeSwitchFunctions.setVideoPlayerRef(videoEditorPlayer);
            croppingFunctions.setVideoPlayerRef(videoEditorPlayer);

            // 步骤 4: 生命周期钩子
            onMounted(() => {
                console.log(`${logPrefix} Vue组件已挂载`);

                // 关键修复：正确设置video元素引用到所有需要的地方
                setTimeout(() => {
                    // 1. 设置到videoEditorState
                    videoEditorState.videoEditorPlayer.value = videoEditorPlayer.value;

                    // 2. 设置到所有功能模块（确保它们能找到video元素）
                    videoPlaybackFunctions.setVideoPlayerRef(videoEditorPlayer);
                    segmentFunctions.setVideoPlayerRef(videoEditorPlayer);
                    modeSwitchFunctions.setVideoPlayerRef(videoEditorPlayer);
                    croppingFunctions.setVideoPlayerRef(videoEditorPlayer);

                    // 3. 设置全局引用（供video-editor.js中的函数使用）
                    window.videoEditorPlayerGlobal = videoEditorPlayer.value;

                    console.log(`${logPrefix} video元素引用已设置:`, {
                        videoEditorPlayerExists: !!videoEditorPlayer.value,
                        videoSrc: videoEditorPlayer.value?.src || '未设置',
                        globalRefExists: !!window.videoEditorPlayerGlobal
                    });

                    // 4. 初始化视频编辑器数据
                    initializeVideoEditorData();
                }, 50); // 延迟50ms确保DOM完全渲染

                // 监听全局初始化事件
                const handleInitializeVideoEditor = (event) => {
                    console.log(`${logPrefix} 收到视频编辑器初始化事件:`, event.detail);
                    // 重新设置video元素引用
                    if (videoEditorPlayer.value) {
                        window.videoEditorPlayerGlobal = videoEditorPlayer.value;
                        videoPlaybackFunctions.setVideoPlayerRef(videoEditorPlayer);
                        segmentFunctions.setVideoPlayerRef(videoEditorPlayer);
                        modeSwitchFunctions.setVideoPlayerRef(videoEditorPlayer);
                        croppingFunctions.setVideoPlayerRef(videoEditorPlayer);
                    }
                    initializeVideoEditorData();
                };
                window.addEventListener('initializeVideoEditor', handleInitializeVideoEditor);

                // 清理事件监听器
                onUnmounted(() => {
                    window.removeEventListener('initializeVideoEditor', handleInitializeVideoEditor);
                });

                // 在 Vue 应用挂载完成后初始化视频编辑器数据
                initializeVideoEditorData();
                
                console.log(`${logPrefix} 视频编辑器数据初始化完成`);
            });

            // 视频编辑器数据初始化函数
            const initializeVideoEditorData = () => {
                const logPrefix = '[文件：app.js][initializeVideoEditorData]';
                console.log(`${logPrefix} 开始初始化视频编辑器数据`);
                
                try {
                    // 🆕 检查是否正在退出编辑模式，如果是则跳过初始化
                    if (window.isExitingEditMode) {
                        console.log(`${logPrefix} 检测到正在退出编辑模式，跳过片段初始化`);
                        return;
                    }

                    // 初始化片段数据
                    if (videoEditorState.clipSegments.value.length === 0) {
                        videoEditorState.clipSegments.value.push({ startTime: null, endTime: null });
                        console.log(`${logPrefix} 初始化第一个片段`);
                    }
                    
                    // 初始化视频配置
                    if (!videoConfig.value.repeatModes || videoConfig.value.repeatModes.length === 0) {
                        videoConfig.value.repeatModes = RepeatModeManager.getDefaultModeSequence(videoConfig.value.repeatCount || 3);
                        console.log(`${logPrefix} 初始化视频配置 repeatModes:`, JSON.stringify(videoConfig.value.repeatModes, null, 2));
                    }

                    // 初始化背景风格配置
                    if (!videoConfig.value.backgroundStyle) {
                        videoConfig.value.backgroundStyle = "newspaper";
                        console.log(`${logPrefix} 初始化背景风格配置 backgroundStyle: ${videoConfig.value.backgroundStyle}`);
                    }
                    
                    // 初始化字幕配置
                    if (!videoConfig.value.subtitleConfig) {
                        console.log('[DEBUG][videoConfig][initializeVideoEditorData] subtitleConfig 不存在，开始初始化');
                        videoConfig.value.subtitleConfig = {
                            videoGuide: {
                                enabled: true,
                                title1: "坚持30天",
                                title2: "听懂国外新闻"
                            },
                            advertisement: {
                                enabled: true,
                                titles: [
                                    {
                                        line1: "🌍关注水蜜桃英语，",
                                        line2: "每天2分钟，听全球要闻！"
                                    },
                                    {
                                        line1: "🌍关注水蜜桃英语，",
                                        line2: "每天2分钟，听力大提升！"
                                    },
                                    {
                                        line1: "🌍关注水蜜桃英语，",
                                        line2: "不靠字幕，听懂世界声！"
                                    }
                                ]
                            }
                        };
                        console.log(`${logPrefix} 初始化字幕配置`);
                        console.log('[DEBUG][videoConfig][initializeVideoEditorData] 初始化后的 subtitleConfig:', JSON.stringify(videoConfig.value.subtitleConfig, null, 2));
                    } else {
                        console.log('[DEBUG][videoConfig][initializeVideoEditorData] subtitleConfig 已存在，跳过初始化');
                        console.log('[DEBUG][videoConfig][initializeVideoEditorData] 现有 subtitleConfig:', JSON.stringify(videoConfig.value.subtitleConfig, null, 2));
                    }
                    
                    // 解析和设置字幕数据
                    if (editorData.value && editorData.value.englishSrtContent) {
                        console.log(`${logPrefix} 开始解析英文字幕数据`);
                        console.log(`${logPrefix} SRT内容长度: ${editorData.value.englishSrtContent.length}`);
                        console.log(`${logPrefix} SRT内容前200字符:`, editorData.value.englishSrtContent.substring(0, 200));
                        try {
                            // 使用 utils.js 中的 parseSrt 函数解析字幕
                            const parsedSubtitles = window.Utils.parseSrt(editorData.value.englishSrtContent);
                            videoEditorState.englishSubtitles.value = parsedSubtitles;
                            console.log(`${logPrefix} ✅ 英文字幕解析完成，共 ${parsedSubtitles.length} 条字幕`);
                            console.log(`${logPrefix} 字幕示例:`, parsedSubtitles.slice(0, 3));
                            
                            // 验证字幕状态是否正确设置
                            setTimeout(() => {
                                console.log(`${logPrefix} 验证字幕状态设置:`, {
                                    englishSubtitlesLength: videoEditorState.englishSubtitles.value.length,
                                    currentSubtitlesValue: videoEditorState.currentSubtitles.value,
                                    firstSubtitle: videoEditorState.englishSubtitles.value[0]
                                });
                            }, 100);
                        } catch (error) {
                            console.error(`${logPrefix} ❌ 字幕解析失败:`, error);
                            videoEditorState.englishSubtitles.value = [];
                        }
                    } else {
                        console.log(`${logPrefix} 没有字幕数据需要解析`);
                        console.log(`${logPrefix} editorData状态:`, {
                            hasEditorData: !!editorData.value,
                            hasEnglishSrtContent: !!(editorData.value && editorData.value.englishSrtContent),
                            editorDataKeys: editorData.value ? Object.keys(editorData.value) : []
                        });
                        videoEditorState.englishSubtitles.value = [];
                    }
                    
                    console.log(`${logPrefix} ✅ 视频编辑器数据初始化完成`);
                } catch (error) {
                    console.error(`${logPrefix} ❌ 视频编辑器数据初始化失败:`, error);
                }
            };

            // 保持向前兼容
            const initializeSubtitles = initializeVideoEditorData;

            // 🆕 退出编辑主处理函数
            const handleExitEditing = async () => {
                const functionLogPrefix = `${logPrefix}[handleExitEditing]`;

                try {
                    console.log(`${functionLogPrefix} ==================== 开始退出编辑流程 ====================`);

                    // 0. 记录退出前的状态
                    logAllEditorStates('退出前');

                    // 1. 检查当前模式并进行相应处理
                    console.log(`${functionLogPrefix} 🔍 步骤1: 检查当前编辑模式`);
                    console.log(`${functionLogPrefix} 当前编辑模式: ${videoEditorState.editorMode.value}`);
                    console.log(`${functionLogPrefix} 是否正在生成: ${state.isGenerating.value}`);

                    // 1.1 画面裁剪模式安全检查（双重保护）
                    if (videoEditorState.editorMode.value === 'frame-cropping') {
                        console.log(`${functionLogPrefix} ⚠️ 检测到画面裁剪模式，按钮应该已被禁用`);
                        console.log(`${functionLogPrefix} 这是一个安全检查，正常情况下不应该执行到这里`);

                        ElementPlus.ElMessage.warning({
                            message: '画面裁剪模式下无法退出编辑，请先切换到其他模式',
                            duration: 3000
                        });
                        return;
                    }

                    // 1.2 根据模式选择确认对话框
                    let confirmed = false;
                    if (state.isGenerating.value) {
                        console.log(`${functionLogPrefix} 🔄 生成模式下退出，显示生成专用确认对话框`);
                        confirmed = await showGenerationExitConfirmation();
                    } else {
                        console.log(`${functionLogPrefix} 📝 常规编辑模式下退出，显示标准确认对话框`);
                        confirmed = await showExitConfirmation();
                    }

                    if (!confirmed) {
                        console.log(`${functionLogPrefix} 用户取消退出编辑`);
                        return;
                    }

                    // 2. 设置退出状态
                    state.isExitingEdit.value = true;
                    console.log(`${functionLogPrefix} 开始执行退出编辑操作`);

                    // 3. 分步骤重置所有状态
                    console.log(`${functionLogPrefix} 步骤1: 重置视频播放器状态`);
                    resetVideoPlayerStates();
                    logAllEditorStates('重置视频播放器后');

                    console.log(`${functionLogPrefix} 步骤2: 重置片段选择状态`);
                    resetSegmentStates();
                    logAllEditorStates('重置片段选择后');

                    console.log(`${functionLogPrefix} 步骤3: 重置裁剪功能状态`);
                    resetCroppingStates();
                    logAllEditorStates('重置裁剪功能后');

                    console.log(`${functionLogPrefix} 步骤4: 重置预览功能状态`);
                    resetPreviewStates();
                    logAllEditorStates('重置预览功能后');

                    console.log(`${functionLogPrefix} 步骤5: 🎛️ 重置参数设置状态 (最重要)`);
                    resetParameterSettingsStates();
                    logAllEditorStates('重置参数设置后');

                    console.log(`${functionLogPrefix} 步骤6: 重置字幕显示状态`);
                    resetSubtitleStates();
                    logAllEditorStates('重置字幕显示后');

                    console.log(`${functionLogPrefix} 步骤7: 🔄 重置生成状态和中断通讯`);
                    resetGenerationStates();
                    logAllEditorStates('重置生成状态后');

                    console.log(`${functionLogPrefix} 步骤8: 清理DOM元素`);
                    cleanupDOMElements();

                    // 等待一下确保DOM清理完成
                    await new Promise(resolve => setTimeout(resolve, 100));

                    console.log(`${functionLogPrefix} 步骤9: 重置主应用状态`);
                    resetMainAppStates();
                    logAllEditorStates('重置主应用状态后');

                    // 最终状态检查
                    logAllEditorStates('最终状态');

                    // 4. 显示成功提示
                    ElementPlus.ElMessage.success({
                        message: '✅ 已退出编辑模式',
                        duration: 2000
                    });

                    console.log(`${functionLogPrefix} 退出编辑流程完成`);

                } catch (error) {
                    console.error(`${functionLogPrefix} 退出编辑失败:`, error);

                    ElementPlus.ElMessage.error({
                        message: `❌ 退出编辑失败: ${error.message}`,
                        duration: 3000
                    });

                } finally {
                    // 5. 重置退出状态
                    state.isExitingEdit.value = false;
                }
            };

            // 视频生成相关函数
            const handleStartGeneration = async () => {
                const logPrefix = '[文件：app.js][handleStartGeneration]';
                console.log(`${logPrefix} 开始视频生成流程`);
                
                try {
                    // 验证生成数据
                    const validateGenerationData = () => {
                        if (!editorData.value) {
                            throw new Error('没有视频数据');
                        }
                        if (!videoEditorState.clipSegments.value || videoEditorState.clipSegments.value.length === 0) {
                            throw new Error('没有选择视频片段');
                        }
                        const validSegments = videoEditorState.clipSegments.value.filter(s => s.startTime !== null && s.endTime !== null);
                        if (validSegments.length === 0) {
                            throw new Error('没有完整的视频片段');
                        }
                        if (!videoEditorState.confirmedCropParams.value.isConfirmed) {
                            throw new Error('请先确认裁剪参数');
                        }
                        return true;
                    };

                    // 验证数据
                    validateGenerationData();

                    // 设置生成状态
                    isGenerating.value = true;
                    generationStatusMessages.value = [];

                    // 构建生成参数
                    const generateParams = {
                        videoIdentifier: editorData.value.videoIdentifier,
                        originalVideoPath: editorData.value.originalVideoPath,
                        clipSegments: videoEditorState.clipSegments.value.filter(s => s.startTime !== null && s.endTime !== null),
                    cropData: {
                            cropWidth: videoEditorState.confirmedCropParams.value.width,
                            cropHeight: videoEditorState.confirmedCropParams.value.height,
                            cropXOffset: videoEditorState.confirmedCropParams.value.x,
                            cropYOffset: videoEditorState.confirmedCropParams.value.y
                        },
                        videoConfig: videoConfig.value,
                        savePath: editorData.value.savePath || '/tmp'
                    };

                    console.log(`${logPrefix} 生成参数构建完成`);
                    console.log('[DEBUG][videoConfig][handleStartGeneration] ========== 发送到后端的 videoConfig ==========');
                    console.log('[DEBUG][videoConfig][handleStartGeneration] 完整的 videoConfig:', JSON.stringify(videoConfig.value, null, 2));
                    console.log('[DEBUG][videoConfig][handleStartGeneration] videoConfig 结构检查:');
                    console.log('[DEBUG][videoConfig][handleStartGeneration] - repeatCount:', videoConfig.value.repeatCount);
                    console.log('[DEBUG][videoConfig][handleStartGeneration] - backgroundStyle:', videoConfig.value.backgroundStyle);
                    console.log('[DEBUG][videoConfig][handleStartGeneration] - repeatModes 数量:', videoConfig.value.repeatModes?.length || 0);
                    console.log('[DEBUG][videoConfig][handleStartGeneration] - repeatModes 详情:', JSON.stringify(videoConfig.value.repeatModes, null, 2));
                    console.log('[DEBUG][videoConfig][handleStartGeneration] - subtitleConfig 存在:', !!videoConfig.value.subtitleConfig);
                    
                    if (videoConfig.value.subtitleConfig) {
                        console.log('[DEBUG][videoConfig][handleStartGeneration] - subtitleConfig 键:', Object.keys(videoConfig.value.subtitleConfig));
                        console.log('[DEBUG][videoConfig][handleStartGeneration] - videoGuide 存在:', !!videoConfig.value.subtitleConfig.videoGuide);
                        console.log('[DEBUG][videoConfig][handleStartGeneration] - advertisement 存在:', !!videoConfig.value.subtitleConfig.advertisement);
                        
                        if (videoConfig.value.subtitleConfig.videoGuide) {
                            console.log('[DEBUG][videoConfig][handleStartGeneration] - videoGuide 完整配置:', JSON.stringify(videoConfig.value.subtitleConfig.videoGuide, null, 2));
                        }
                        
                        if (videoConfig.value.subtitleConfig.advertisement) {
                            console.log('[DEBUG][videoConfig][handleStartGeneration] - advertisement 完整配置:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement, null, 2));
                            console.log('[DEBUG][videoConfig][handleStartGeneration] - advertisement.titles 数量:', videoConfig.value.subtitleConfig.advertisement.titles?.length || 0);
                        }
                    } else {
                        console.log('[DEBUG][videoConfig][handleStartGeneration] - subtitleConfig 为空或未定义');
                    }
                    
                    console.log('[DEBUG][videoConfig][handleStartGeneration] 完整的 generateParams:', JSON.stringify(generateParams, null, 2));
                    console.log('[DEBUG][videoConfig][handleStartGeneration] ========================================');

                    console.log(`${logPrefix} 🚀 开始调用 generateVideo API，参数:`, generateParams);
                    
                    const result = await window.Utils.postApi('/api/video/generateVideo', generateParams);
                    
                    console.log(`${logPrefix} 🎯 API调用完成，返回结果:`, result);
                    console.log(`${logPrefix} 🔍 result 类型检查:`, {
                        type: typeof result,
                        isNull: result === null,
                        isUndefined: result === undefined,
                        constructor: result?.constructor?.name,
                        hasOwnProperty: result?.hasOwnProperty ? 'yes' : 'no'
                    });
                    
                    console.log(`${logPrefix} 生成完成:`, result);
                    
                    // 更新状态
                    isGenerating.value = false;
                    isGenerationCompleted.value = true;

                    // 更新下载链接 - 支持多种结果格式
                    console.log(`${logPrefix} 开始处理下载链接`);
                    console.log(`${logPrefix} 🔍 完整结果内容:`, JSON.stringify(result, null, 2));

                    let enhancedSubtitlePath = null;
                    let finalVideoPath = null;
                    let actualResult = result;

                    // 检查是否需要从 finalContextPreview 解析真正的结果
                    if (result && result.finalContextPreview && typeof result.finalContextPreview === 'string') {
                        try {
                            console.log(`${logPrefix} 🔍 检测到 finalContextPreview 字符串，尝试解析...`);
                            console.log(`${logPrefix} 🔍 finalContextPreview 内容:`, result.finalContextPreview);
                            
                            const parsedContext = JSON.parse(result.finalContextPreview);
                            console.log(`${logPrefix} ✅ finalContextPreview 解析成功:`, parsedContext);
                            
                            // 使用解析后的上下文作为实际结果
                            actualResult = parsedContext;
                        } catch (parseError) {
                            console.error(`${logPrefix} ❌ finalContextPreview 解析失败:`, parseError);
                            console.log(`${logPrefix} 继续使用原始结果结构`);
                        }
                    }

                    // 直接检查所有可能的路径（使用解析后的结果）
                    const possiblePaths = {
                        // 从解析后的结果中查找
                        'actualResult.enhancedBilingualSubtitleJsonPath': actualResult?.enhancedBilingualSubtitleJsonPath,
                        'actualResult.finalVideoPath': actualResult?.finalVideoPath,
                        // 标准化结果格式
                        'actualResult.files.enhancedBilingualSubtitleJsonPath': actualResult?.files?.enhancedBilingualSubtitleJsonPath,
                        'actualResult.files.finalVideoPath': actualResult?.files?.finalVideoPath,
                        // 原始上下文格式
                        'actualResult.context.enhancedBilingualSubtitleJsonPath': actualResult?.context?.enhancedBilingualSubtitleJsonPath,
                        'actualResult.context.finalVideoPath': actualResult?.context?.finalVideoPath,
                        // 原始结果格式（备用）
                        'result.files.enhancedBilingualSubtitleJsonPath': result?.files?.enhancedBilingualSubtitleJsonPath,
                        'result.files.finalVideoPath': result?.files?.finalVideoPath,
                        'result.context.enhancedBilingualSubtitleJsonPath': result?.context?.enhancedBilingualSubtitleJsonPath,
                        'result.context.finalVideoPath': result?.context?.finalVideoPath
                    };

                    console.log(`${logPrefix} 🔍 所有可能路径检查:`, possiblePaths);

                    // 查找字幕文件（优先从解析后的结果查找）
                    enhancedSubtitlePath = actualResult?.enhancedBilingualSubtitleJsonPath || 
                                          actualResult?.files?.enhancedBilingualSubtitleJsonPath ||
                                          actualResult?.context?.enhancedBilingualSubtitleJsonPath ||
                                          result?.files?.enhancedBilingualSubtitleJsonPath ||
                                          result?.context?.enhancedBilingualSubtitleJsonPath ||
                                          result?.originalContext?.enhancedBilingualSubtitleJsonPath ||
                                          null;

                    // 查找视频文件（优先从解析后的结果查找）
                    finalVideoPath = actualResult?.finalVideoPath || 
                                    actualResult?.files?.finalVideoPath ||
                                    actualResult?.context?.finalVideoPath ||
                                    result?.files?.finalVideoPath ||
                                    result?.context?.finalVideoPath ||
                                    result?.originalContext?.finalVideoPath ||
                                    null;

                    console.log(`${logPrefix} 🎯 最终查找结果:`, {
                        enhancedSubtitlePath,
                        finalVideoPath,
                        usedParsedContext: actualResult !== result
                    });

                    // 设置下载链接状态
                    if (enhancedSubtitlePath) {
                        downloadLinks.value.enhancedBilingualSubtitle = enhancedSubtitlePath;
                        console.log(`${logPrefix} ✅ 字幕下载链接已设置: ${enhancedSubtitlePath}`);
                    } else {
                        console.warn(`${logPrefix} ⚠️ 未找到字幕文件路径`);
                    }

                    if (finalVideoPath) {
                        downloadLinks.value.finalVideo = finalVideoPath;
                        console.log(`${logPrefix} ✅ 视频下载链接已设置: ${finalVideoPath}`);
                    } else {
                        console.warn(`${logPrefix} ⚠️ 未找到视频文件路径`);
                    }

                    console.log(`${logPrefix} 最终下载链接状态:`, {
                        enhancedBilingualSubtitle: downloadLinks.value.enhancedBilingualSubtitle,
                        finalVideo: downloadLinks.value.finalVideo
                    });

                    // 显示成功消息
                    if (window.ElementPlus && window.ElementPlus.ElMessage) {
                        if (enhancedSubtitlePath && finalVideoPath) {
                            window.ElementPlus.ElMessage.success('视频生成完成！字幕和视频文件已准备好下载。');
                        } else if (finalVideoPath) {
                            window.ElementPlus.ElMessage.success('视频生成完成！视频文件已准备好下载。');
                    } else {
                            window.ElementPlus.ElMessage.warning('视频生成完成，但下载链接可能不完整。');
                        }
                    }

                } catch (error) {
                    console.error(`${logPrefix} 生成失败:`, error);
                    isGenerating.value = false;
                    
                    // 显示错误消息
                    if (window.ElementPlus && window.ElementPlus.ElMessage) {
                        window.ElementPlus.ElMessage.error(`生成失败: ${error.message}`);
                    } else {
                        alert(`生成失败: ${error.message}`);
                    }
                }
            };

            const handleRestartEditing = () => {
                const logPrefix = '[文件：app.js][handleRestartEditing]';
                console.log(`${logPrefix} 重新开始编辑`);
                
                // 重置生成状态
                isGenerating.value = false;
                isGenerationCompleted.value = false;
                isGenerationFailed.value = false;

                // 重置SSE状态
                sseStatus.value = {
                    connection: {
                        isConnected: false,
                        connectionId: null,
                        startTime: null,
                        lastHeartbeat: null,
                        errorCount: 0
                    },
                    pipeline: {
                        name: null,
                        status: null,
                        progress: 0,
                        startTime: null,
                        endTime: null,
                        errorDetails: null
                    },
                    currentTask: {
                        name: null,
                        status: null,
                        detail: null,
                        startTime: null,
                        errorInfo: null
                    },
                    taskHistory: []
                };
                
                // 清空下载链接和画面裁剪参数
                downloadLinks.value.enhancedBilingualSubtitle = null;
                downloadLinks.value.finalVideo = null;
                
                // 清空画面裁剪参数
                    videoEditorState.confirmedCropParams.value = {
                        isConfirmed: false,
                        x: null,
                        y: null,
                        width: null,
                        height: null
                    };

                // 重置到片段截取模式
                navigateToSegmentClipping();
                
                console.log(`${logPrefix} 已重置到编辑模式`);
                console.log(`${logPrefix} 画面裁剪参数已清空:`, videoEditorState.confirmedCropParams.value);
            };

            const updateVideoConfig = () => {
                const logPrefix = '[文件：app.js][updateVideoConfig]';
                console.log(`${logPrefix} 视频配置更新开始`);
                console.log('[DEBUG][videoConfig][updateVideoConfig] 更新前的完整配置:', JSON.stringify(videoConfig.value, null, 2));
                
                // 确保重复模式数组长度匹配重复次数
                adjustRepeatModesLength();
                
                console.log('[DEBUG][videoConfig][updateVideoConfig] 更新后的完整配置:', JSON.stringify(videoConfig.value, null, 2));
                console.log('[DEBUG][videoConfig][updateVideoConfig] 配置检查:');
                console.log('[DEBUG][videoConfig][updateVideoConfig] - backgroundStyle:', videoConfig.value.backgroundStyle);
                console.log('[DEBUG][videoConfig][updateVideoConfig] - videoGuide存在:', !!videoConfig.value.subtitleConfig?.videoGuide);
                console.log('[DEBUG][videoConfig][updateVideoConfig] - advertisement存在:', !!videoConfig.value.subtitleConfig?.advertisement);
                if (videoConfig.value.subtitleConfig?.videoGuide) {
                    console.log('[DEBUG][videoConfig][updateVideoConfig] - videoGuide详情:', JSON.stringify(videoConfig.value.subtitleConfig.videoGuide, null, 2));
                }
                if (videoConfig.value.subtitleConfig?.advertisement) {
                    console.log('[DEBUG][videoConfig][updateVideoConfig] - advertisement详情:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement, null, 2));
                }
            };

            // 新增：重复次数变化处理函数
            const updateRepeatCount = (newCount) => {
                const logPrefix = '[文件：app.js][updateRepeatCount]';
                console.log(`${logPrefix} 重复次数变化: ${videoConfig.value.repeatCount} -> ${newCount}`);
                
                if (newCount < 1 || newCount > 5) {
                    console.warn(`${logPrefix} 无效的重复次数: ${newCount}`);
                    return;
                }
                
                const oldCount = videoConfig.value.repeatCount;
                videoConfig.value.repeatCount = newCount;
                
                // 调整 repeatModes 数组长度
                adjustRepeatModesLength();
                
                console.log(`${logPrefix} 重复次数更新完成: ${oldCount} -> ${newCount}`);
                console.log(`${logPrefix} repeatModes 数组长度: ${videoConfig.value.repeatModes.length}`);
                console.log(`${logPrefix} 更新后的 repeatModes:`, JSON.stringify(videoConfig.value.repeatModes, null, 2));
            };

            // 新增：调整重复模式数组长度的函数
            const adjustRepeatModesLength = () => {
                const logPrefix = '[文件：app.js][adjustRepeatModesLength]';
                const targetCount = videoConfig.value.repeatCount;
                const currentCount = videoConfig.value.repeatModes.length;
                
                console.log(`${logPrefix} 调整数组长度: 当前=${currentCount}, 目标=${targetCount}`);
                
                if (currentCount < targetCount) {
                    // 需要增加元素
                    for (let i = currentCount; i < targetCount; i++) {
                        const newMode = RepeatModeManager.createNewMode(i);
                        videoConfig.value.repeatModes.push(newMode);
                        console.log(`${logPrefix} 添加新模式[${i}]:`, newMode);
                    }
                } else if (currentCount > targetCount) {
                    // 需要删除元素
                    const removedModes = videoConfig.value.repeatModes.splice(targetCount);
                    console.log(`${logPrefix} 删除多余模式:`, removedModes);
                }
                
                // 重新生成所有显示文本（确保序号正确）
                videoConfig.value.repeatModes = RepeatModeManager.regenerateDisplayTexts(videoConfig.value.repeatModes);
                
                console.log(`${logPrefix} 数组长度调整完成: ${videoConfig.value.repeatModes.length}`);
            };

            // 新增：更新单个重复模式的函数
            const updateRepeatMode = (index, newModeName) => {
                const logPrefix = '[文件：app.js][updateRepeatMode]';
                console.log(`${logPrefix} 更新模式[${index}]: ${videoConfig.value.repeatModes[index]?.name} -> ${newModeName}`);
                
                if (index < 0 || index >= videoConfig.value.repeatModes.length) {
                    console.warn(`${logPrefix} 无效的索引: ${index}`);
                    return;
                }

                if (!RepeatModeManager.AVAILABLE_MODES[newModeName]) {
                    console.warn(`${logPrefix} 无效的模式名称: ${newModeName}`);
                    return;
                }
                
                const oldMode = { ...videoConfig.value.repeatModes[index] };
                videoConfig.value.repeatModes[index].name = newModeName;
                videoConfig.value.repeatModes[index].displayText = RepeatModeManager.generateDisplayText(index, newModeName);
                
                console.log(`${logPrefix} 模式更新完成:`, {
                    index,
                    oldMode,
                    newMode: videoConfig.value.repeatModes[index]
                });
            };

            // 新增：重置为默认配置的函数
            const resetToDefaultConfig = () => {
                const logPrefix = '[文件：app.js][resetToDefaultConfig]';
                console.log(`${logPrefix} 重置为默认配置`);
                
                // 重置重复配置
                videoConfig.value.repeatCount = 3;
                videoConfig.value.repeatModes = RepeatModeManager.getDefaultModeSequence(3);

                // 重置背景风格配置
                videoConfig.value.backgroundStyle = "newspaper";
                
                // 重置字幕配置
                videoConfig.value.subtitleConfig = {
                    videoGuide: {
                        enabled: true,
                        title1: "坚持30天",
                        title2: "听懂国外新闻"
                    },
                    advertisement: {
                        enabled: true,
                        titles: [
                            {
                                line1: "关注🌍水蜜桃英语",
                                line2: "每天2分钟，听全球要闻！"
                            },
                            {
                                line1: "关注🌍水蜜桃英语",
                                line2: "摆脱字幕，听力涨得快！"
                            },
                            {
                                line1: "关注🌍水蜜桃英语",
                                line2: "不靠字幕，听懂世界声！"
                            }
                        ]
                    }
                };
                
                console.log(`${logPrefix} 默认配置重置完成:`, JSON.stringify(videoConfig.value, null, 2));
                
                // 显示成功消息
                if (window.ElementPlus && window.ElementPlus.ElMessage) {
                    window.ElementPlus.ElMessage.success('配置已重置为默认值');
                }
            };

            // 广告标题管理函数
            const addAdvertisementTitle = () => {
                const logPrefix = '[文件：app.js][addAdvertisementTitle]';
                console.log(`${logPrefix} 添加新的广告标题`);
                console.log('[DEBUG][videoConfig][addAdvertisementTitle] 添加前的 advertisement.titles:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement.titles, null, 2));
                
                if (!videoConfig.value.subtitleConfig.advertisement.titles) {
                    console.log('[DEBUG][videoConfig][addAdvertisementTitle] titles 数组不存在，创建新数组');
                    videoConfig.value.subtitleConfig.advertisement.titles = [];
                }
                
                const newTitle = {
                    line1: "新的广告标题第一行",
                    line2: "新的广告标题第二行"
                };
                videoConfig.value.subtitleConfig.advertisement.titles.push(newTitle);
                
                console.log(`${logPrefix} 广告标题已添加，当前总数: ${videoConfig.value.subtitleConfig.advertisement.titles.length}`);
                console.log('[DEBUG][videoConfig][addAdvertisementTitle] 新添加的标题:', JSON.stringify(newTitle, null, 2));
                console.log('[DEBUG][videoConfig][addAdvertisementTitle] 添加后的 advertisement.titles:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement.titles, null, 2));
            };

            const removeAdvertisementTitle = (index) => {
                const logPrefix = '[文件：app.js][removeAdvertisementTitle]';
                console.log(`${logPrefix} 删除广告标题，索引: ${index}`);
                console.log('[DEBUG][videoConfig][removeAdvertisementTitle] 删除前的 advertisement.titles:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement.titles, null, 2));
                
                if (videoConfig.value.subtitleConfig.advertisement.titles && 
                    index >= 0 && 
                    index < videoConfig.value.subtitleConfig.advertisement.titles.length) {
                    
                    const removedTitle = videoConfig.value.subtitleConfig.advertisement.titles[index];
                    videoConfig.value.subtitleConfig.advertisement.titles.splice(index, 1);
                    console.log(`${logPrefix} 广告标题已删除，当前总数: ${videoConfig.value.subtitleConfig.advertisement.titles.length}`);
                    console.log('[DEBUG][videoConfig][removeAdvertisementTitle] 被删除的标题:', JSON.stringify(removedTitle, null, 2));
                    console.log('[DEBUG][videoConfig][removeAdvertisementTitle] 删除后的 advertisement.titles:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement.titles, null, 2));
                    } else {
                    console.warn(`${logPrefix} 无效的索引: ${index}`);
                    console.log('[DEBUG][videoConfig][removeAdvertisementTitle] 当前数组长度:', videoConfig.value.subtitleConfig.advertisement.titles?.length || 0);
                }
            };

            const updateAdvertisementTitle = (index, field, value) => {
                const logPrefix = '[文件：app.js][updateAdvertisementTitle]';
                console.log(`${logPrefix} 更新广告标题，索引: ${index}, 字段: ${field}, 值: ${value}`);
                console.log('[DEBUG][videoConfig][updateAdvertisementTitle] 更新前的标题:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement.titles[index], null, 2));
                
                if (videoConfig.value.subtitleConfig.advertisement.titles && 
                    index >= 0 && 
                    index < videoConfig.value.subtitleConfig.advertisement.titles.length &&
                    (field === 'line1' || field === 'line2')) {
                    
                    const oldValue = videoConfig.value.subtitleConfig.advertisement.titles[index][field];
                    videoConfig.value.subtitleConfig.advertisement.titles[index][field] = value;
                    console.log(`${logPrefix} 广告标题已更新`);
                    console.log('[DEBUG][videoConfig][updateAdvertisementTitle] 字段值变化:', `${oldValue} -> ${value}`);
                    console.log('[DEBUG][videoConfig][updateAdvertisementTitle] 更新后的标题:', JSON.stringify(videoConfig.value.subtitleConfig.advertisement.titles[index], null, 2));
                } else {
                    console.warn(`${logPrefix} 无效的参数: index=${index}, field=${field}`);
                    console.log('[DEBUG][videoConfig][updateAdvertisementTitle] 当前数组长度:', videoConfig.value.subtitleConfig.advertisement.titles?.length || 0);
                    console.log('[DEBUG][videoConfig][updateAdvertisementTitle] 有效字段:', ['line1', 'line2']);
                }
            };

            // 新增：背景风格更新函数
            const updateBackgroundStyle = (newStyle) => {
                const logPrefix = '[文件：app.js][updateBackgroundStyle]';
                console.log(`${logPrefix} 更新背景风格: ${videoConfig.value.backgroundStyle} -> ${newStyle}`);

                // 验证背景风格值的有效性
                const validStyles = ['newspaper', 'abstract'];
                if (!validStyles.includes(newStyle)) {
                    console.warn(`${logPrefix} 无效的背景风格: ${newStyle}，有效值: ${validStyles.join(', ')}`);
                    return;
                }

                const oldStyle = videoConfig.value.backgroundStyle;
                videoConfig.value.backgroundStyle = newStyle;

                console.log(`${logPrefix} 背景风格更新完成: ${oldStyle} -> ${newStyle}`);
                console.log('[DEBUG][videoConfig][updateBackgroundStyle] 更新后的完整配置:', JSON.stringify(videoConfig.value, null, 2));
            };

            // SSE状态文本转换函数
            const getPipelineStatusText = (status) => {
                const statusMap = {
                    'running': '🔄 运行中',
                    'completed': '✅ 已完成',
                    'failed': '❌ 失败',
                    'ACCEPTED': '📥 已接受'
                };
                return statusMap[status] || status || '未知状态';
            };

            const getTaskStatusText = (status) => {
                const statusMap = {
                    'started': '🚀 已开始',
                    'processing': '⚙️ 处理中',
                    'processing_ffmpeg_started': '🎬 FFmpeg处理开始',
                    'processing_ffmpeg_completed': '🎬 FFmpeg处理完成',
                    'processing_api_call_started': '📡 API调用开始',
                    'processing_api_call_completed': '📡 API调用完成',
                    'completed': '✅ 已完成',
                    'failed': '❌ 失败',
                    'failed_ffmpeg': '❌ FFmpeg失败',
                    'failed_api_call': '❌ API调用失败'
                };
                return statusMap[status] || status || '未知状态';
            };

            // 确保返回所有需要在模板中使用的数据和方法
            return {
                // state
                activeSection,
                isUploading,
                selectedFile,
                isExitingEdit, // 🆕 退出编辑中状态

                // 项目选择状态
                projectList,
                selectedProject,
                isLoadingProjects,
                projectLoadError,
                showProjectSelectionDialog,  // 🆕 项目选择对话框显示状态
                dialogSelectedProject,       // 🆕 对话框中选中的项目
                projectPagination,

                editorData,
                uploadStatusMessages,
                generationStatusMessages,
                isGenerating,
                isGenerationCompleted,
                isGenerationFailed,
                downloadLinks,
                videoConfig,
                sseStatus,

                // 生成视频查看对话框状态
                showGeneratedVideosDialog,
                generatedVideosList,
                selectedVideos,
                selectAllVideos,
                currentProjectId,
                loadingGeneratedVideos,
                videoStatusMap,
                markingAsPublished,
                batchDownloading,
                generatingLiveVideoDraft,

                // computed
                editingIndicatorText,
                isParameterSettingsEnabled,
                isGenerationEnabled,
                getCurrentTime,
                exitButtonState, // 🆕 退出按钮状态控制
                
                // video editor state and computed
                ...videoEditorState,
                ...videoEditorComputed,

                // 显式暴露字幕相关状态，确保模板可以访问
                currentSubtitles: videoEditorState.currentSubtitles,
                englishSubtitles: videoEditorState.englishSubtitles,

                // 别名引用，便于模板中使用
                clipSegments: videoEditorState.clipSegments,
                confirmedCropParams: videoEditorState.confirmedCropParams,
                
                // 新增：图像收集和裁剪流程控制状态（显式暴露以确保模板可访问）
                isCollectingImages: videoEditorState.isCollectingImages,
                isImageCollectionReady: videoEditorState.isImageCollectionReady,

                // methods
                triggerFileInput,
                uploadVideo,
                navigateTo,

                // 项目管理方法
                initializeProjectListModule,  // 🆕 添加模板初始化函数
                loadProjectList,
                selectProject,
                resetProjectSelectionStates,  // 🆕 添加状态重置函数
                clearProjectSelection,
                confirmProjectSelection,
                renameProject,  // 🆕 添加项目重命名函数
                deleteProject,

                // 生成视频查看方法
                openGeneratedVideosDialog,
                closeGeneratedVideosDialog,
                toggleVideoSelection,
                toggleSelectAll,
                getVideoStatus,
                getVideoStatusText,
                getVideoStatusType,
                markSelectedAsPublished,
                batchDownloadSelected,
                generateLiveVideoDraft,

                // 🆕 退出编辑方法
                handleExitEditing,
                handleStartGeneration,
                handleRestartEditing,
                updateVideoConfig,
                updateRepeatCount,
                updateRepeatMode,
                resetToDefaultConfig,
                addAdvertisementTitle,
                removeAdvertisementTitle,
                updateAdvertisementTitle,
                updateBackgroundStyle,
                getPipelineStatusText,
                getTaskStatusText,

                // video editor methods
                ...videoPlaybackFunctions,
                ...segmentFunctions,
                ...modeSwitchFunctions,
                ...croppingFunctions,

                // 工具函数（使Utils在模板中可用）
                Utils: window.Utils,

                // refs for elements
                fileInput,
                videoEditorPlayer,

                // Element Plus Icons
                ElementPlusIconsVue: window.ElementPlusIconsVue
            };
        }
    });

    console.log(`${logPrefix} Vue应用实例创建完成`);
    return app;
}

// ============================================================================
// 5. 应用启动模块
// ============================================================================

/**
 * @功能概述: 将核心功能挂载到全局的AppCore对象上
 */
function initializeAppCore() {
    const logPrefix = '[文件：app.js][initializeAppCore]';
    console.log(`${logPrefix} 开始初始化AppCore`);
    
    if (!window.VideoEditor) {
        console.error(`${logPrefix} ❌ VideoEditor模块未加载`);
        return;
    }

    window.AppCore = {
        createVueApp,
        startApp
    };
    
    console.log(`${logPrefix} ✅ AppCore初始化完成`, window.AppCore);
}

/**
 * @功能概述: 应用启动函数
 */
function startApp() {
    const logPrefix = '[文件：app.js][startApp]';
    console.log(`${logPrefix} 开始启动应用`);

    try {
        if (!window.AppCore || !window.AppCore.createVueApp) {
            console.error(`${logPrefix} ❌ AppCore或createVueApp未定义，无法启动Vue应用`);
            return;
        }

        const app = window.AppCore.createVueApp();

        // 注册Element Plus组件
        app.component('el-container', ElContainer);
        app.component('el-header', ElHeader);
        app.component('el-main', ElMain);
        app.component('el-button', ElButton);
        app.component('el-button-group', ElButtonGroup);
        app.component('el-progress', ElProgress);
        app.component('el-select', ElSelect);
        app.component('el-option', ElOption);
        app.component('el-slider', ElSlider);
        app.component('el-icon', ElementPlus.ElIcon);
        app.component('el-switch', ElSwitch);
        app.component('el-input', ElInput);
        app.component('el-dialog', ElDialog);
        app.component('el-checkbox', ElCheckbox);
        app.component('el-tag', ElTag);

        // 注册所有Element Plus图标
        for (const [key, component] of Object.entries(window.ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.mount('#app');
        console.log(`${logPrefix} ✅ Vue应用已成功挂载到#app`);

    } catch (error) {
        console.error(`${logPrefix} ❌ Vue应用启动失败:`, error);
        // 可以将错误信息显示在页面上，方便调试
        const appElement = document.getElementById('app');
        if (appElement) {
            appElement.innerHTML = `<div style="color: red; padding: 20px;">
                <h3>应用启动失败</h3>
                <p>${error.message}</p>
                <pre>${error.stack}</pre>
            </div>`;
        }
    }
}

// 初始化AppCore
initializeAppCore();