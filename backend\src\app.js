/**
 * @功能概述: Express 应用的入口文件，设置中间件、路由和错误处理。
 */

// 导入必要的模块
const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid'); // 导入 uuid 库的 v4 方法，并重命名为 uuidv4
const path = require('path'); // path 模块不再需要，因为我们不提供复杂的静态文件服务
// 导入日志配置
const logger = require('./utils/logger'); // logger 工具模块已存在
const fs = require('fs'); // 确保 fs 被引入，如果需要写入日志文件

// 导入配置模块和视频路由，重新启用相关功能
const config = require('./config'); // 修正：直接导入已初始化的配置对象
const videoRoutes = require('./routes/videoRoutes'); // 导入视频路由


const app = express(); // 创建 Express 应用实例
// 定义日志前缀，在此处定义，确保req.id可用性，如果不可用则标记为'app-init'
const appLogPrefix = `[文件：app.js][Express应用][初始化]`; // 模块级别/应用初始化日志前缀

logger.info(`${appLogPrefix} Express 应用开始初始化...`); // 日志：应用初始化开始


/**
 * @功能概述: 请求ID生成中间件。
 * @描述: 此中间件为每个进入的HTTP请求生成一个唯一的UUID (版本4)，
 *        并将其附加到请求对象(req)的 `id` 属性上 (即 `req.id`)。
 *        这个唯一的请求ID可以在后续的日志记录、错误追踪和请求生命周期管理中非常有用，
 *        尤其是在复杂的应用或微服务架构中，它可以帮助将分散的日志条目关联到同一个请求。
 *        此中间件应该在其他路由和业务逻辑处理中间件之前尽早注册，以确保 `req.id` 在整个请求处理链中都可用。
 * @param {object} req - Express 请求对象。
 * @param {object} res - Express 响应对象。
 * @param {function} next - 调用下一个中间件的函数。
 */
app.use((req, res, next) => {
  req.id = uuidv4(); // 为请求对象生成并设置一个唯一的 v4 UUID
  // 记录已生成请求ID的调试日志，包含生成的ID本身
  logger.debug(`${appLogPrefix}[请求ID中间件] 已为请求生成ID: ${req.id}`);
  next(); // 将控制权传递给下一个中间件
});
logger.info(`${appLogPrefix} 请求ID生成中间件已启用。`); // 日志：请求ID中间件配置完成


/**
 * @功能概述: 启用 CORS (跨域资源共享) 中间件。
 * @描述: CORS 是一种浏览器安全机制，它允许运行在一个域上的 Web 应用程序访问另一个域上的资源。
 *        例如，如果你的前端应用运行在 `http://localhost:3001`，而后端 API 运行在 `http://localhost:3000`，
 *        没有 CORS 配置，浏览器会阻止前端向后端发送请求，因为它们是不同的"源"（协议、域名、端口任一不同）。
 *        `cors()` 中间件会设置必要的 HTTP 响应头（如 `Access-Control-Allow-Origin`），
 *        告诉浏览器允许来自不同源的请求。
 * @示例:
 *        假设前端应用在 `http://localhost:3001`，尝试请求 `http://localhost:3000/api/video`。
 *        如果没有 `cors()` 中间件，浏览器会抛出跨域错误。
 *        启用 `cors()` 后，后端响应会包含 `Access-Control-Allow-Origin: *` (默认情况下允许所有源)，
 *        或者配置为 `Access-Control-Allow-Origin: http://localhost:3001`，
 *        从而允许前端成功获取数据。
 */
app.use(cors());
logger.info(`${appLogPrefix} CORS 中间件已启用。`); // 日志：CORS配置完成




/**
 * @功能概述: 解析 JSON 格式的请求体。
 * @描述: `express.json()` 是一个内置的中间件函数，它解析传入的具有 JSON 负载的请求。
 *        当客户端（如前端应用）发送 `Content-Type: application/json` 的请求时，
 *        此中间件会读取请求体中的 JSON 数据，并将其解析成 JavaScript 对象，
 *        然后将这个对象附加到 `req.body` 属性上。
 *        如果没有这个中间件，`req.body` 将会是 `undefined`，无法直接访问 JSON 数据。
 * @示例:
 *        如果客户端发送 `POST /api/data` 请求，请求体为 `{"name": "Alice", "age": 30}`，
 *        启用此中间件后，在后续的路由处理器中，你可以通过 `req.body.name` 访问到 "Alice"，
 *        通过 `req.body.age` 访问到 30。
 */
app.use(express.json());
logger.info(`${appLogPrefix} JSON 请求体解析中间件已启用。`); // 日志：JSON解析配置完成


/**
 * @功能概述: 解析 URL-encoded 请求体。
 * @描述: `express.urlencoded()` 是一个内置的中间件函数，它解析传入的具有 URL-encoded 负载的请求。
 *        当客户端（如前端应用）发送 `Content-Type: application/x-www-form-urlencoded` 的请求时，
 *        此中间件会读取请求体中的 URL-encoded 数据，并将其解析成 JavaScript 对象，
 *        然后将这个对象附加到 `req.body` 属性上。
 *        如果没有这个中间件，`req.body` 将会是 `undefined`，无法直接访问 URL-encoded 数据。
 * @示例:
 *        如果客户端发送 `POST /api/data` 请求，请求体为 `name=Alice&age=30`，
 *        启用此中间件后，在后续的路由处理器中，你可以通过 `req.body.name` 访问到 "Alice"，
 *        通过 `req.body.age` 访问到 30。
 */
app.use(express.urlencoded({ extended: true }));
logger.info(`${appLogPrefix} URL-encoded 请求体解析中间件已启用。`); // 日志：URL编码解析配置完成


// 静态文件服务中间件
// 拼接 'frontend-test' 目录的绝对路径
const frontendTestPath = path.join(__dirname, '..', '..', 'frontend-test');
// 使用 express.static 中间件来提供 'frontend-test' 目录中的静态文件服务
// 例如，当浏览器请求 /js/app.js 时，它会尝试从 projectRoot/frontend-test/js/app.js 提供文件
app.use(express.static(frontendTestPath));
logger.info(`${appLogPrefix} 已配置静态文件服务，指向目录: ${frontendTestPath}`);


/**
 * @功能概述: 提供对后端上传文件的静态访问。
 * @描述: 配置多个静态文件服务路径，支持新的项目目录架构和向后兼容。
 */
const uploadsPath = path.join(__dirname, '..', 'uploads'); // Correctly resolves to projectRoot/backend/uploads

// 新的项目文件静态访问路径
app.use('/api/files/projects', express.static(path.join(uploadsPath, 'projects')));
logger.info(`${appLogPrefix} 已配置项目文件静态服务，将URL路径 /api/files/projects 映射到物理目录: ${path.join(uploadsPath, 'projects')}`);

// 保留原有的静态文件访问路径（向后兼容）
app.use('/backend/uploads', express.static(uploadsPath));
logger.info(`${appLogPrefix} 已配置传统静态文件服务，将URL路径 /backend/uploads 映射到物理目录: ${uploadsPath}`);


/**
 * @功能概述: 处理根路径 ("/") 的GET请求，返回一个简单的 "Hello World" HTML页面。
 * @param {object} req - Express 请求对象。
 * @param {object} res - Express 响应对象。
 * @returns {void} - 通过 res.send() 发送HTML响应。
 */
app.get('/', (req, res) => {
    // 步骤 D.1.2.1.1: 实现该路由，使其返回一个包含文本 "Hello World" 的简单 HTML 页面。
    // 为此特定请求定义日志前缀，如果req.id存在则使用，否则使用通用ID
    const reqId = req.id || 'unknown-root-request';
    const routeLogPrefix = `[文件：app.js][根路由][/][GET][ReqID:${reqId}]`;

    logger.info(`${routeLogPrefix} 接收到根路径请求。`); // 日志：记录接收到请求
    // 返回 frontend-test 目录下的 index.html 文件
    res.sendFile(path.join(frontendTestPath, 'index.html'));
    logger.info(`${routeLogPrefix} 已发送 "frontend-test/index.html" 作为响应。`); // 日志：记录已发送响应
});
logger.info(`${appLogPrefix} 根路径 ("/") GET 请求处理器已配置。`); // 日志：根路由配置完成



/**
 * @功能概述: 挂载视频路由到 /api/video 路径下。
 * @描述: 将视频路由挂载到 /api/video 路径下，以便通过该路径访问视频处理相关的 API。
 *        例如，客户端可以发送 `POST /api/video/upload` 请求来上传视频文件。
 * @示例:
 *        假设客户端发送 `POST /api/video/upload` 请求，请求体为视频文件数据，
 *        启用此挂载后，在后续的路由处理器中，你可以通过 `req.body` 访问到视频文件数据。
 */
logger.info(`${appLogPrefix} 开始挂载视频路由...`); // 日志：视频路由挂载开始
app.use('/api/video', videoRoutes); // 挂载视频路由到 /api/video 路径下
logger.info(`${appLogPrefix} 视频路由已成功挂载到 /api/video`); // 日志：视频路由挂载完成

/**
 * @功能概述: 挂载下载路由到 /api 路径下。
 * @描述: 提供文件下载服务，支持视频和音频文件的安全下载。
 */
const { downloadFile, getFileInfo } = require('./controllers/download/downloadController');
logger.info(`${appLogPrefix} 开始挂载下载路由...`); // 日志：下载路由挂载开始
app.get('/api/download', downloadFile); // 文件下载端点
app.get('/api/fileinfo', getFileInfo); // 文件信息查询端点
logger.info(`${appLogPrefix} 下载路由已成功挂载到 /api/download 和 /api/fileinfo`); // 日志：下载路由挂载完成





/**
 * @功能概述: 导出 Express 应用实例 (如果其他模块需要引用它)
 * @描述: 导出 app 实例，以便其他模块可以引用它。
 * @示例:
 *        假设在另一个文件中需要使用 app 实例，可以通过 `const app = require('./app');` 导入。
 */
module.exports = app; // 重新启用导出
logger.info(`${appLogPrefix} Express 应用实例已导出。`); // 日志: 应用实例导出





/**
 * @功能概述: 全局错误处理机制。
 * @描述:
 *   此代码块配置了 Node.js 进程的全局错误监听器，用于捕获两种主要类型的未处理错误：
 *   1. `uncaughtException`: 捕获同步代码中发生的、未被任何 `try...catch` 块捕获的异常。
 *      当此类异常发生时，Node.js 进程通常处于不确定状态，因此最佳实践是记录错误并安全退出进程。
 *   2. `unhandledRejection`: 捕获异步操作（Promise）中发生的、未被 `.catch()` 方法处理的拒绝。
 *      虽然不像 `uncaughtException` 那样立即导致进程崩溃，但未处理的 Promise 拒绝也表明代码中存在逻辑错误，
 *      必须记录以便调试。在未来的 Node.js 版本中，未处理的 Promise 拒绝也可能导致进程终止。
 *
 * @为什么需要:
 *   这些监听器是构建健壮 Node.js 应用的关键部分，它们确保即使在发生意外错误时，应用也能：
 *   - 记录详细的错误信息，便于问题排查。
 *   - 对于严重错误（如 `uncaughtException`），能够以受控的方式退出，避免僵尸进程或数据损坏。
 *   - 提高应用的可靠性和可维护性。
 *
 * @简单示例 (概念性):
 *   假设有以下代码：
 *
 *   // 1. uncaughtException 示例：
 *   // 如果在某个地方不小心写了 `console.log(undefinedVariable.property);`
 *   // 且没有 `try...catch` 包裹，那么 `uncaughtException` 监听器就会捕获到这个错误。
 *   // 例如：
 *   // function riskySyncOperation() {
 *   //   throw new Error('这是一个未捕获的同步错误！');
 *   // }
 *   // riskySyncOperation(); // 如果没有 try/catch，此处的错误会被 uncaughtException 捕获
 *
 *   // 2. unhandledRejection 示例：
 *   // 如果你创建了一个 Promise，但它拒绝了，并且你没有为它添加 `.catch()` 处理：
 *   // 例如：
 *   // new Promise((resolve, reject) => {
 *   //   reject(new Error('这是一个未处理的 Promise 拒绝！'));
 *   // }); // 此处的拒绝会被 unhandledRejection 监听器捕获
 *   //
 *   // 或者一个异步函数抛出错误但没有被 await/try-catch 捕获：
 *   // async function riskyAsyncOperation() {
 *   //   throw new Error('异步函数内部的错误！');
 *   // }
 *   // riskyAsyncOperation(); // 如果没有 await 或 .catch()，此处的错误会被 unhandledRejection 监听器捕获
 *
 *   通过这些监听器，即使上述错误发生，应用也能记录下来并采取适当的行动（如退出进程）。
 */
process.on('uncaughtException', (error, origin) => {
  const timestamp = new Date().toISOString();
  const errorLog = `!!!!!!!!!!!!!! 全局未捕获异常 !!!!!!!!!!!!!!\n时间戳: ${timestamp}\n错误: ${error.stack || error}\n来源: ${origin}\n-------------------------------------------------------\n\n`;
  // 使用 logger 记录未捕获的异常，假设 logger 已配置处理控制台和文件输出
  logger.error(errorLog);
  process.exit(1); // 对于未捕获的异常，通常建议立即干净地退出
});

process.on('unhandledRejection', (reason, promise) => {
  const timestamp = new Date().toISOString();
  const rejectionLog = `!!!!!!!!!!!!!! 全局未处理的Promise拒绝 !!!!!!!!!!!!!!\n时间戳: ${timestamp}\n原因: ${reason instanceof Error ? reason.stack : reason}\n-------------------------------------------------------\n\n`;
  // 使用 logger 记录未处理的 Promise 拒绝，假设 logger 已配置处理控制台和文件输出
  logger.error(rejectionLog);
  // 对于未处理的 Promise 拒绝，不总是需要强制退出，但记录是必须的
  // 根据 Node.js 的未来版本行为，未处理的拒绝也可能导致进程终止
});




// 从配置中获取端口号，如果配置中没有指定，使用8081端口避免端口冲突
const PORT = config && config.port ? config.port : 8081;

// 启动 Express 应用，监听指定的端口
const server = app.listen(PORT, () => {
    // 记录应用成功启动的日志信息，并显示监听的端口号
    logger.info(`${appLogPrefix} Express 应用已成功启动，正在监听端口 ${PORT}`); 
    // 提示用户如何通过浏览器访问 "Hello World" 页面（通常是根路径）
    logger.info(`${appLogPrefix} 您可以通过访问 http://localhost:${PORT}/ 来访问 "Hello World" 页面。`); 
    // 提示用户如何访问视频处理 API 的端点
    logger.info(`${appLogPrefix} 您可以通过访问 http://localhost:${PORT}/api/video 来访问视频处理 API。`); 
});

// 增加服务器超时设置 (10分钟)
server.timeout = 600000; 

