/**
 * @功能概述: 测试Remotion TSX生成任务
 * @测试类型: 任务单元测试
 * @测试目标: 验证TSX文件生成和Root.tsx更新功能
 */

const path = require('path');
const fs = require('fs').promises;
const RemotionTSXGeneratorTask = require('../RemotionTSXGeneratorTask');
const RemotionTemplateService = require('../../services/RemotionTemplateService');
const logger = require('../../utils/logger');

// 测试配置
const TEST_CONFIG = {
    templatePath: path.join(__dirname, '../../remotion/templates/video-template.json'),
    generatedDir: path.join(__dirname, '../../remotion/generated'),
    rootPath: path.join(__dirname, '../../remotion/Root.tsx')
};

/**
 * @功能概述: 执行TSX生成任务测试
 * @执行流程:
 *   1. 加载模板配置
 *   2. 创建TSX生成任务
 *   3. 执行任务生成TSX文件
 *   4. 验证生成结果
 *   5. 检查Root.tsx更新
 */
async function testRemotionTSXGenerator() {
    const testLogPrefix = '[测试：TSX生成任务]';
    
    try {
        console.log(`${testLogPrefix} 开始测试Remotion TSX生成功能`);
        
        // 步骤 1: 加载模板配置
        console.log(`${testLogPrefix} 加载模板配置...`);
        const templateService = new RemotionTemplateService('test-tsx-gen');
        const templateConfig = await templateService.loadTemplate('video-template');
        
        console.log(`${testLogPrefix} ✅ 模板配置加载成功`);
        console.log(`${testLogPrefix}   - 组合ID: ${templateConfig.composition.id}`);
        console.log(`${testLogPrefix}   - 视频尺寸: ${templateConfig.composition.width}x${templateConfig.composition.height}`);
        console.log(`${testLogPrefix}   - 时长: ${templateConfig.composition.durationInFrames}帧`);
        
        // 步骤 2: 创建TSX生成任务
        console.log(`${testLogPrefix} 创建TSX生成任务...`);
        const tsxGeneratorTask = new RemotionTSXGeneratorTask('test-tsx-gen');
        
        // 步骤 3: 准备任务上下文
        const context = {
            templateConfig: templateConfig
        };
        
        // 进度回调函数
        const progressCallback = (data) => {
            console.log(`${testLogPrefix} [进度] ${data.status}: ${data.detail}`);
        };
        
        // 步骤 4: 执行任务
        console.log(`${testLogPrefix} 执行TSX生成任务...`);
        const result = await tsxGeneratorTask.execute(context, progressCallback);
        
        // 步骤 5: 验证生成结果
        console.log(`${testLogPrefix} 验证生成结果...`);
        
        // 检查生成的TSX文件是否存在
        const tsxFileExists = await fs.access(result.generatedTSXPath).then(() => true).catch(() => false);
        if (!tsxFileExists) {
            throw new Error(`生成的TSX文件不存在: ${result.generatedTSXPath}`);
        }
        
        // 读取生成的TSX文件内容
        const tsxContent = await fs.readFile(result.generatedTSXPath, 'utf8');
        if (!tsxContent.includes(templateConfig.composition.id)) {
            throw new Error('生成的TSX文件不包含正确的组件名');
        }
        
        if (!tsxContent.includes(templateConfig.background.fallback.color)) {
            throw new Error('生成的TSX文件不包含正确的背景颜色');
        }
        
        console.log(`${testLogPrefix} ✅ TSX文件生成验证通过`);
        console.log(`${testLogPrefix}   - 文件路径: ${result.generatedTSXPath}`);
        console.log(`${testLogPrefix}   - 组合ID: ${result.compositionId}`);
        
        // 步骤 6: 验证Root.tsx更新
        console.log(`${testLogPrefix} 验证Root.tsx更新...`);
        const rootContent = await fs.readFile(TEST_CONFIG.rootPath, 'utf8');
        
        if (!rootContent.includes(templateConfig.composition.id)) {
            throw new Error('Root.tsx未正确更新组合ID');
        }
        
        if (!rootContent.includes(`durationInFrames={${templateConfig.composition.durationInFrames}}`)) {
            throw new Error('Root.tsx未正确更新时长配置');
        }
        
        console.log(`${testLogPrefix} ✅ Root.tsx更新验证通过`);
        
        // 步骤 7: 显示生成的文件内容预览
        console.log(`${testLogPrefix} 生成的TSX文件内容预览:`);
        console.log('------- TSX内容开始 -------');
        console.log(tsxContent.split('\n').slice(0, 20).join('\n')); // 显示前20行
        console.log('------- TSX内容结束 -------');
        
        // 步骤 8: 显示文件列表
        console.log(`${testLogPrefix} generated目录文件列表:`);
        const generatedFiles = await fs.readdir(TEST_CONFIG.generatedDir);
        generatedFiles.forEach(file => {
            console.log(`${testLogPrefix}   - ${file}`);
        });
        
        console.log(`${testLogPrefix} 🎉 所有测试通过！`);
        console.log(`${testLogPrefix} 💡 现在可以在Remotion Studio中查看生成的视频效果`);
        console.log(`${testLogPrefix} 💡 运行命令: npm run remotion`);
        
        return true;
        
    } catch (error) {
        console.error(`${testLogPrefix} ❌ 测试失败: ${error.message}`);
        console.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        return false;
    }
}

// 执行测试
if (require.main === module) {
    testRemotionTSXGenerator()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('测试执行异常:', error);
            process.exit(1);
        });
}

module.exports = { testRemotionTSXGenerator };
