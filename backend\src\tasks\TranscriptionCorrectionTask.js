// 导入TaskBase基类，提供标准化的任务执行接口和进度监控能力
const TaskBase = require('../class/TaskBase');
// 导入日志工具，用于记录任务执行过程中的关键信息
const logger = require('../utils/logger');
// 导入字幕处理工具，用于数据提取和SRT生成 (注: subtitleProcessor在此文件中实际未被直接调用，但其功能概念相关)
// const subtitleProcessor = require('../utils/subtitleProcessor'); 
// 导入LLM服务，用于转录文本的智能校正
const llmService = require('../services/llmService');


// 导入配置加载器，用于获取LLM相关配置
const config = require('../config');

// 导入标准化的进度监控常量，用于统一的状态管理
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');
// 导入JSON校验与修复工具
const { extractAndParseJson } = require('../utils/jsonValidator');
// 导入文件保存工具，用于保存中间和最终结果文件
const fileSaver = require('../utils/fileSaver');
// 导入 path 模块，用于处理文件路径
const path = require('path');
// 导入 fs 模块，用于文件系统操作（如存在性检查，如果需要在Task中处理）
const fs = require('fs');

// 模块级日志前缀，用于标识从本文件输出的日志
const taskModuleLogPrefix = '[文件：TranscriptionCorrectionTask.js][转录校正任务][模块初始化]';

// 记录模块加载成功的日志
logger.info(`${taskModuleLogPrefix}模块已加载。`);


class TranscriptionCorrectionTask extends TaskBase {




    /**
     * @功能概述: 构造函数，创建转录校正任务实例
     * @param {string} [name='TranscriptionCorrectionTask'] - 任务名称，用于日志标识和进度追踪
     * 
     * @说明: 调用父类构造函数初始化基础属性，设置实例级日志前缀
     */
    constructor(name = 'TranscriptionCorrectionTask') {
        super(name); // 调用 TaskBase 构造函数，初始化任务ID、状态等基础属性
        // 设置实例级日志前缀，包含文件名、任务类型和实例名称
        this.instanceLogPrefix = `[文件：TranscriptionCorrectionTask.js][转录校正任务][${this.name}]`;
        // 记录任务实例创建成功的日志
        logger.info(`${this.instanceLogPrefix} TranscriptionCorrectionTask 实例已创建。`);
    }

 

 
    async execute(context, progressCallback) {

        // 步骤 1: 优先从上下文中解构出核心参数。
        const { videoIdentifier, simplifiedSubtitleJsonArray: inputSimplifiedSubtitleJsonArray, savePath, reqId } = context; // 从SubtitleOptimizationTask接收已优化的数据

        // 步骤 2: 定义任务执行所必需的字段列表。
        // 注意：'savePath' 也应被视为必需字段，如果文件保存是此任务的核心功能。
        // correctedFullText 是任务的输出，不应该作为输入要求
        const requiredFields = ['reqId', 'videoIdentifier', 'simplifiedSubtitleJsonArray', 'savePath'];

        // 步骤 3: 构建执行日志前缀。
        // 使用解构后的 videoIdentifier 和 reqId (如果存在)，如果未定义则提供默认值。
        const execLogPrefix = `[文件：TranscriptionCorrectionTask.js][校正任务][${videoIdentifier || 'unknown_video'}][REQ:${reqId || 'unknown_req'}]`;

        // 步骤 4: 验证必需的上下文参数。
        // this.validateRequiredFields 方法会检查 context 对象是否直接包含这些字段。
        this.validateRequiredFields(context, requiredFields, execLogPrefix);

        // [操作]: 设置进度回调函数，用于向上层报告任务执行进度。
        this.setProgressCallback(progressCallback);
        // [操作]: 使用标准化方法标记任务开始，自动报告STARTED状态。
        this.start();



        // [初始化]: 声明并初始化所有任务处理结果相关的变量。
        let simplifiedSubtitleJsonArray = null; // LLM校正后的简化字幕JSON数组 (输出给下游任务)
        let simplifiedSubtitleJsonPath = null; // 校正后简化字幕JSON文件路径 (输出给下游任务)
        let englishSrtContent = null; // 生成的英文字幕SRT内容
        let englishSrtPath = null; // 英文SRT文件路径
        let correctedFullText = null; // 校正后的完整文本（用于LLM上下文理解）



        try {

             

            // 步骤 2: 执行LLM批量校正处理 (基于已优化的简化字幕数据)。
            // [说明]: 此方法负责调用LLM对已经优化过的简化字幕数据进行校正。
            simplifiedSubtitleJsonArray = await this.performCorrectionAndExtraction(inputSimplifiedSubtitleJsonArray, execLogPrefix);

            // [检查]: 验证LLM校正后的数据是否存在且为有效的数组。
            if (!simplifiedSubtitleJsonArray || !Array.isArray(simplifiedSubtitleJsonArray)) {
                 const errorMsg = 'LLM校正后的简化字幕数据无效或为空。';
                logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
                throw new Error(errorMsg);
            }

            // 步骤 3: 保存校正后的简化字幕JSON文件。
            // [进度]: 报告任务进度：正在保存校正后的简化字幕JSON文件。
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_SIMPLIFIED_JSON, {
                 detail: '正在保存校正后的简化字幕JSON文件',
                 current: 70,
                 total: 100,
                 technicalDetail: '调用 saveSimplifiedSubtitleJson'
            });

            // [操作]: 调用方法保存校正后的简化字幕JSON文件。
            logger.info(`${execLogPrefix}[步骤 3.0] 开始保存校正后的简化字幕JSON文件。`);
            simplifiedSubtitleJsonPath = await this.saveSimplifiedSubtitleJson(simplifiedSubtitleJsonArray, videoIdentifier, execLogPrefix, savePath);
            logger.info(`${execLogPrefix}[步骤 3.1] 校正后的简化字幕JSON文件保存成功。路径: ${simplifiedSubtitleJsonPath}。`);

            // 步骤 4: 生成英文字幕SRT内容 (基于校正后的简化JSON)。
            // [进度]: 报告任务进度：正在生成英文字幕SRT内容。
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.GENERATING_SRT, {
                detail: '正在生成英文字幕SRT内容',
                current: 85,
                total: 100,
                technicalDetail: '调用 generateEnglishSRT (基于校正后的简化JSON)'
            });
            logger.info(`${execLogPrefix}[步骤 4.0] 开始生成英文字幕SRT内容。`);
            englishSrtContent = this.generateEnglishSRT(simplifiedSubtitleJsonArray, execLogPrefix);
            logger.info(`${execLogPrefix}[步骤 4.1] 英文字幕SRT内容生成成功。长度: ${englishSrtContent.length} 字符。`);

            // 步骤 5: 保存英文字幕SRT文件。
            // [进度]: 报告任务进度：正在保存英文字幕SRT文件。
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_SRT, {
                detail: '正在保存英文字幕SRT文件',
                current: 95,
                total: 100,
                technicalDetail: `调用 saveEnglishSRT | 视频标识符:${videoIdentifier} | 文件名:${videoIdentifier}_corrected_english_subtitle.srt`
            });

            // [操作]: 调用方法保存英文字幕SRT文件。
            logger.info(`${execLogPrefix}[步骤 5.0] 开始保存英文字幕SRT文件。视频标识符: ${videoIdentifier}, 内容长度: ${englishSrtContent.length} 字节。`);
            englishSrtPath = await this.saveEnglishSRT(
                englishSrtContent,    // 参数1: SRT内容
                videoIdentifier,      // 参数2: 视频标识符
                execLogPrefix,         // 参数3: 日志前缀
                savePath
            );
            logger.info(`${execLogPrefix}[步骤 5.1] 英文字幕SRT文件保存成功。路径: ${englishSrtPath}, 文件大小: ${englishSrtContent.length} 字节。`);

            // 步骤 6: 生成 correctedFullText 和 fullTranscriptText 字段 (从校正后的简化字幕数据生成)。
            // [检查]: 确保校正后的数据存在且为数组，以便生成完整文本。
            let fullTranscriptText = '';
            if (simplifiedSubtitleJsonArray && Array.isArray(simplifiedSubtitleJsonArray)) {
                 // correctedFullText 用于LLM上下文理解
                 correctedFullText = simplifiedSubtitleJsonArray.map(s => s.text).join(' ').trim();
                 // fullTranscriptText 用于下游ContentSummarizationTask
                 fullTranscriptText = correctedFullText; // 两者内容相同，但用途不同
                 logger.info(`${execLogPrefix}[步骤 6.0] 已从校正后的简化字幕数据生成 correctedFullText 和 fullTranscriptText 字段。`);
            } else {
                 logger.warn(`${execLogPrefix}[步骤 6.0][WARN] 无法从校正后的简化字幕数据生成完整文本，数据无效或缺失。`);
                 correctedFullText = '';
                 fullTranscriptText = '';
            }

            // 步骤 7: 整理并输出任务结果。
            // [操作]: 将所有最终处理结果添加到上下文对象中。
            context.simplifiedSubtitleJsonArray = simplifiedSubtitleJsonArray;
            context.simplifiedSubtitleJsonPath = simplifiedSubtitleJsonPath;
            context.englishSrtContent = englishSrtContent;
            context.englishSrtPath = englishSrtPath;
            context.fullTranscriptText = fullTranscriptText;
     
            // [操作]: 构建任务结果对象，包含所有关键输出信息。
            const result = {
                correctionStatus: 'success',
                simplifiedSubtitleJsonArray: simplifiedSubtitleJsonArray,
                simplifiedSubtitleJsonPath: simplifiedSubtitleJsonPath,
                englishSrtContent: englishSrtContent,
                englishSrtPath: englishSrtPath,
                fullTranscriptText: fullTranscriptText,
            };

            // [操作]: 使用标准化方法标记任务完成，自动报告COMPLETED状态并返回结果。
            this.complete(result);
            logger.info(`${execLogPrefix}[步骤 8.0] 转录校正任务执行成功并完成。`);

            // [返回]: 返回处理结果，该结果将被合并到流水线的上下文中。
            return result;

        } catch (error) {
            // [错误处理]: 捕获任务执行过程中的所有错误。
            // [操作]: 确保在错误发生时，`execLogPrefix` 已经定义，用于日志追踪。
            const errorLogPrefix = typeof execLogPrefix !== 'undefined' ? execLogPrefix : `${this.instanceLogPrefix}[REQ:${context.reqId}][文件处理前错误]`;
            logger.error(`${errorLogPrefix}[ERROR] 任务执行失败: ${error.message}`);
            // [操作]: 使用标准化方法处理错误，自动设置失败状态、记录错误信息、调用进度回调。
            this.fail(error);
            // [操作]: 重新抛出错误，通知上层任务失败。
            throw error;
        }
    }



    
 
    async performCorrectionAndExtraction(simplifiedSubtitleJsonArray, execLogPrefix) {

        /*
         * 对输入的简化字幕数据进行有效性检查。
         * 确保数组存在且不为空。这些数据来自SubtitleOptimizationTask，
         * 已经经过初步处理和优化。如果数据无效，记录错误并抛出异常。
         */
        if (!Array.isArray(simplifiedSubtitleJsonArray) || simplifiedSubtitleJsonArray.length === 0) {
             const errorMsg = 'context 中缺少有效的 simplifiedSubtitleJsonArray 数据或数组为空。';
             logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
             logger.debug(`${execLogPrefix}[DEBUG] 无效 simplifiedSubtitleJsonArray 结构预览: ${JSON.stringify(simplifiedSubtitleJsonArray).substring(0, 500)}...`);
             throw new Error(errorMsg);
        }

        /*
         * 报告当前任务的进度。
         * 此时任务处于"运行中"状态，子状态为"正在处理提取的数据"。
         * 进度条显示为 10/100，表示这是数据处理的早期阶段。
         * 详细描述了正在进行的具体操作：检查原始 apiResponse 结构。
         * 这里的进度报告是初步的，后续的校验和修复工作将在 LLM 处理步骤中进行。
         */
        this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING_EXTRACTION, {
            detail: '正在处理原始转录响应数据',
            current: 10,
            total: 100,
            technicalDetail: '检查原始 apiResponse 结构'
        });



        

        // correctedSubtitleArray: 用于存储经过LLM校正或降级处理后的简化字幕数据。初始为 null。
        let correctedSubtitleArray = null;
        // useOriginalData: 标记是否需要回退到使用原始简化字幕数据（当LLM处理失败时）。初始为 false。
        let useOriginalData = false;


        // 步骤 2.2: 尝试调用 LLM 进行校正
        try {
            logger.info(`${execLogPrefix}[步骤 2.2] 尝试调用 LLM 进行字幕校正。`);
            // 调用 callLLMForCorrection，传递简化字幕数据
            const batchCorrectionResponse = await this.callLLMForCorrection(simplifiedSubtitleJsonArray, execLogPrefix);

             // 步骤 2.3: 如果 LLM 调用成功且返回有效数据，则解析 LLM 响应
            if (batchCorrectionResponse && batchCorrectionResponse.status === 'success' && typeof batchCorrectionResponse.processedText === 'string') {
                 logger.info(`${execLogPrefix}[步骤 2.3] LLM 校正调用成功，开始解析响应。`);
                 // 调用 parseLLMResponse，它内部会使用 jsonValidator 并更新 correctedSubtitleArray
                 correctedSubtitleArray = await this.parseLLMResponse(batchCorrectionResponse, execLogPrefix);
                 logger.info(`${execLogPrefix}[步骤 2.3.1] LLM 响应解析完成。字幕条目数量: ${correctedSubtitleArray ? correctedSubtitleArray.length : 'N/A'}`);
            } else {
                 const responseDetails = batchCorrectionResponse ? JSON.stringify(batchCorrectionResponse).substring(0, 200) : '无响应';
                 logger.warn(`${execLogPrefix}[WARN][步骤 2.2] LLM校正未返回成功状态或 processedText 无效。响应: ${responseDetails}...`);
                 useOriginalData = true; // LLM 失败，标记使用原始数据
            }

        } catch (llmError) {
            logger.error(`${execLogPrefix}[ERROR][步骤 2.2] LLM 校正调用失败: ${llmError.message}`);
            logger.warn(`${execLogPrefix}[WARN] LLM 校正失败，将回退到使用原始简化字幕数据继续处理。`);
            useOriginalData = true; // LLM 抛出异常，标记使用原始数据
        }

        // 步骤 2.4: 如果 LLM 失败，则回退到使用原始简化字幕数据（降级方案）
        if (useOriginalData || correctedSubtitleArray === null) { // 如果LLM失败或解析失败，则使用原始数据
            logger.info(`${execLogPrefix}[步骤 2.4] 使用原始简化字幕数据（LLM降级方案）。`);
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, { // 可以复用 PROCESSING 状态
                 detail: '正在使用原始简化字幕数据（LLM降级）',
                 current: useOriginalData ? 70 : 15, // 根据是否是降级调整进度
                 total: 100,
                 technicalDetail: '使用原始简化字幕数据（无LLM校正）'
             });

            try {
                // 直接使用原始的简化字幕数据（已经经过SubtitleOptimizationTask处理）
                // 这些数据已经是有效的简化字幕格式，无需额外的JSON解析
                correctedSubtitleArray = simplifiedSubtitleJsonArray;
                logger.info(`${execLogPrefix}[步骤 2.4.1] 降级方案：直接使用原始简化字幕数据。条目数量: ${correctedSubtitleArray.length}`);

            } catch (originalDataError) {
                 const errorMsg = `处理原始简化字幕数据时发生错误: ${originalDataError.message}`;
                 logger.error(`${execLogPrefix}[ERROR][步骤 2.4] ${errorMsg}`);
                 // 原始数据处理也失败，抛出错误终止任务
                 throw new Error(errorMsg);
            }
        }

        // 确保最终获得了校正后的简化字幕数据
        if (!correctedSubtitleArray || !Array.isArray(correctedSubtitleArray) || correctedSubtitleArray.length === 0) {
             const errorMsg = '未能从 LLM 响应或原始数据中获得有效的简化字幕数组。';
             logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
             throw new Error(errorMsg);
        }

         // 步骤 2.5: 直接返回校正后的简化字幕数组
         // 这个数组可以直接用于后续的SRT生成等处理
         logger.info(`${execLogPrefix}[步骤 2.5] performCorrectionAndExtraction 完成。最终条目数量: ${correctedSubtitleArray.length}`);
        return correctedSubtitleArray; // 直接返回校正后的简化字幕数组
    }





    async callLLMForCorrection(simplifiedSubtitleJsonArray, execLogPrefix) {

        /*
         * 步骤 2.2.1: 验证简化字幕数据是否存在。
         * 这是 LLM 调用的基础，确保有数据可处理。
         * 如果数据无效，则记录警告日志并返回 null，跳过 LLM 校正调用。
         */
        if (!Array.isArray(simplifiedSubtitleJsonArray) || simplifiedSubtitleJsonArray.length === 0) {
            logger.warn(`${execLogPrefix}[WARN] 简化字幕数据无效或为空，跳过 LLM 校正调用。`);
            return null; // 没有数据，无需调用 LLM
        }

        // 使用LLM专用进度报告方法，报告准备阶段
        this.reportLLMProgress('preparing', '准备LLM批量校正请求', {
            current: 30, // 进度更新到30%
            total: 100,
            segmentsCount: simplifiedSubtitleJsonArray.length // 添加数据条目数量信息
        });

        /*
         * 步骤 2.2.1.5: Token优化 - 创建4字段简化版本给LLM处理
         * 保存原始5字段数据用于后续的智能词语对齐
         * 创建简化的4字段版本发送给LLM，减少Token消耗
         */
        // 保存原始的5字段数据，用于后续智能词语对齐
        this.originalFullSegments = [...simplifiedSubtitleJsonArray];
        
        // 创建4字段简化版本（移除words字段）
        const simplifiedForLLM = simplifiedSubtitleJsonArray.map(segment => ({
            id: segment.id,
            start: segment.start,
            end: segment.end,
            text: segment.text
            // 故意移除 words 字段，减少Token消耗
        }));

        logger.info(`${execLogPrefix}[TOKEN优化] 原始5字段数据: ${JSON.stringify(this.originalFullSegments).length} 字符`);
        logger.info(`${execLogPrefix}[TOKEN优化] 简化4字段数据: ${JSON.stringify(simplifiedForLLM).length} 字符`);
        logger.info(`${execLogPrefix}[TOKEN优化] Token节省: ${JSON.stringify(this.originalFullSegments).length - JSON.stringify(simplifiedForLLM).length} 字符 (${(((JSON.stringify(this.originalFullSegments).length - JSON.stringify(simplifiedForLLM).length) / JSON.stringify(this.originalFullSegments).length) * 100).toFixed(1)}%)`);

        /*
         * 准备LLM校正参数
         * 使用简化的4字段版本发送给LLM，以便 LLM 可以处理。
         * 使用 CORRECT_TRANSCRIPTION 任务类型对应的提示词。
         * 使用默认模板。
         * 注意：提示词参数名不能修改，保持向后兼容性。
         */
        const segmentsJsonString = JSON.stringify(simplifiedForLLM);
        const correctionTaskType = 'CORRECT_TRANSCRIPTION'; // 使用 CORRECTION_TRANSCRIPTION 任务类型对应的提示词
        const correctionTemplateName = 'default'; // 默认模板

        const correctionPromptParams = {
            segments_json_array_string: segmentsJsonString,
            // 设置默认语言为英文（来自SubtitleOptimizationTask的数据通常已经是英文）
            language_of_text: 'English',
            // 生成完整文本作为上下文参考（从简化字幕数据中提取）
            correctedFullText: simplifiedSubtitleJsonArray.map(item => item.text || '').join(' ').trim(),
            systemPromptContent: '严格遵守JSON格式要求, 确保输出内容符合JSON格式。'
        };

        // 确定LLM模型
    
        //const correctionModel = 'google/gemini-2.5-flash-preview-05-20'
        const correctionModel = 'google/gemini-2.5-flash-lite-preview-06-17'

        //const correctionModel = 'google/gemini-flash-1.5-8b'
        //const correctionModel = 'deepseek/deepseek-r1-0528:free'


        logger.info(`${execLogPrefix}[步骤 2.2.1] 使用LLM模型进行校正: ${correctionModel}`);
        logger.debug(`${execLogPrefix}[步骤 2.2.2] LLM提示词参数预览: ${JSON.stringify(correctionPromptParams).substring(0, 200)}...`);


        // 构建LLM调用选项 - 使用增强API配置
        const correctionOptions = {
            // === 传统参数（保持不变）===
            promptParams: correctionPromptParams,       // 提示词参数（包含待校正的segments JSON和语言信息）
            templateName: correctionTemplateName,       // 使用的提示词模板名称（当前使用默认模板）
            modelName: correctionModel,                 // 模型名称（根据上下文优先级确定的最终模型）
            temperature: 0.3, // 温度系数（控制输出随机性，0-1范围，0.3平衡准确性与创造性）
            max_tokens: 20000, // 最大token限制（防止长文本溢出，默认2万token）
            forceJsonOutput: true, // 强制JSON输出模式（确保LLM返回结构化数据）
            validateJsonOutput: true, // 明确启用JSON输出的有效性校验
            maxJsonValidationRetries: 2, // 如果首次JSON无效，允许额外最多2次修正尝试
            retryCount: 3,
            retryDelay: 1000,
            
            // === 新增：API增强配置 ===
            apiEnhancements: {
                // OpenRouter Structured Outputs - 强制标准化4字段结构（Token优化）
                structuredOutput: {
                    enabled: true,
                    schema: {
                        name: 'subtitle_correction_response_optimized',
                        strict: true,
                        schema: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: {
                                        type: 'string',
                                        description: 'Unique identifier for the subtitle segment'
                                    },
                                    start: {
                                        type: 'number',
                                        description: 'Start time in seconds (floating point)'
                                    },
                                    end: {
                                        type: 'number',
                                        description: 'End time in seconds (floating point)'
                                    },
                                    text: {
                                        type: 'string',
                                        description: 'Corrected subtitle text content'
                                    }
                                },
                                required: ['id', 'start', 'end', 'text'],
                                additionalProperties: false
                            }
                        }
                    }
                },
                
                // 高级重试策略
                advancedRetry: {
                    exponentialBackoff: true,  // 启用指数退避
                    jitter: true,              // 启用随机抖动
                    maxDelay: 30000           // 最大延迟30秒
                },
                
                // 自定义请求头
                customHeaders: {
                    'X-Task-Type': 'TranscriptionCorrection',
                    'X-Processing-Mode': 'LLM-Enhanced',
                    'X-Schema-Version': 'v1.0'
                },
                
                // Message Transforms - 启用压缩优化
                transforms: ['middle-out']
            }
        };

        logger.info(`${execLogPrefix}[步骤 2.2.3] LLM调用选项已构建，启用增强API模式和结构化输出`);
         // 记录最终的LLM调用参数（传统+增强）
         logger.debug(`${execLogPrefix}[步骤 *******] 传统LLM调用参数: ${JSON.stringify({
             temperature: correctionOptions.temperature,
             max_tokens: correctionOptions.max_tokens,
             forceJsonOutput: correctionOptions.forceJsonOutput,
             modelName: correctionOptions.modelName,
             retryCount: correctionOptions.retryCount
         })}`);
         logger.debug(`${execLogPrefix}[步骤 *******] 增强API功能: ${JSON.stringify({
             structuredOutput: correctionOptions.apiEnhancements.structuredOutput.enabled,
             advancedRetry: `exponentialBackoff=${correctionOptions.apiEnhancements.advancedRetry.exponentialBackoff}`,
             customHeaders: Object.keys(correctionOptions.apiEnhancements.customHeaders).length > 0,
             transforms: correctionOptions.apiEnhancements.transforms.length > 0
         })}`);


        try {
            // 使用LLM专用进度报告方法，报告发送阶段
            this.reportLLMProgress('sending', '发送LLM校正请求', {
                current: 30, // 进度更新到30%
                total: 100,
                modelName: correctionModel // 添加模型名称信息
            });

            logger.info(`${execLogPrefix}[步骤 2.2.4] 开始调用LLM进行批量校正。`);

            // 使用LLM专用进度报告方法，报告等待阶段
            this.reportLLMProgress('waiting', '等待LLM处理校正请求', {
                current: 50, // 进度更新到50%
                total: 100,
                estimatedTimeRemaining: 60 // 预估剩余时间1分钟 (这是一个估计值)
            });

            // 调用LLM进行校正
            const batchCorrectionResponse = await llmService.callLLM(correctionTaskType, correctionOptions);

            // 使用LLM专用进度报告方法，报告接收阶段
            this.reportLLMProgress('receiving', '接收LLM校正响应', {
                current: 60, // 进度更新到60%
                total: 100,
                responseStatus: batchCorrectionResponse?.status, // 添加响应状态
                processedTextLength: batchCorrectionResponse?.processedText?.length || 0 // 添加响应文本长度
            });

            logger.info(`${execLogPrefix}[步骤 2.2.5] 增强LLM调用完成。响应状态: ${batchCorrectionResponse ? batchCorrectionResponse.status : 'N/A'}`);
             logger.debug(`${execLogPrefix}[步骤 2.2.6] LLM 响应文本预览 (前200字符): ${batchCorrectionResponse?.processedText?.substring(0, 200)}...`);
             
             // 记录增强功能使用情况
             if (batchCorrectionResponse?.enhancedFeatures) {
                 logger.info(`${execLogPrefix}[步骤 2.2.7] 增强功能使用情况: ${JSON.stringify(batchCorrectionResponse.enhancedFeatures)}`);
             }


            return batchCorrectionResponse; // 返回LLM原始响应对象

        } catch (llmError) {
             // 在这里只记录错误，不抛出，让 performCorrectionAndExtraction 处理降级
            logger.error(`${execLogPrefix}[ERROR] LLM 校正调用抛出异常: ${llmError.message} Stack: ${llmError.stack}`);
            // 返回 null 或特定的错误标记，以便上层判断是否降级
            return { status: 'failed', error: { message: llmError.message } };
        }
    }



    async parseLLMResponse(batchCorrectionResponse, execLogPrefix) {
        // 使用LLM专用进度报告方法，报告解析阶段
        this.reportLLMProgress('parsing', '解析LLM校正响应', {
            current: 70, // 进度更新到70%
            total: 100,
            responseLength: batchCorrectionResponse.processedText?.length || 0 // 添加响应长度信息
        });

        try {
            logger.debug(`${execLogPrefix}[步骤 2.3.2] 开始使用jsonValidator.extractAndParseJson进行解析。原始响应长度: ${batchCorrectionResponse.processedText?.length || 0}`);
            // 使用 jsonValidator.extractAndParseJson 进行解析和修复尝试
            const validationResult = extractAndParseJson(batchCorrectionResponse.processedText, `${execLogPrefix}[jsonValidator]`);

            logger.debug(`${execLogPrefix}[步骤 2.3.3] jsonValidator解析尝试日志: ${JSON.stringify(validationResult.parsingAttemptsLog, null, 2)}`);

            if (validationResult.success) {
                logger.info(`${execLogPrefix}[步骤 2.3.4] jsonValidator成功解析JSON。`);
                const correctedSegments = validationResult.data;

                // 验证解析结果是否是有效的 segments 数组（4字段结构）
                this.validateSegmentsArray(correctedSegments, execLogPrefix);

                logger.info(`${execLogPrefix}[步骤 2.3.5] LLM 4字段响应解析并校验成功。校正后 segments 数量: ${correctedSegments.length}`);

                /*
                 * 步骤 2.3.6: 智能词语对齐算法 - 将LLM返回的4字段结果补充为5字段
                 * 基于编辑距离算法，将原始words时间戳信息正确映射到修正后的文本
                 */
                logger.info(`${execLogPrefix}[步骤 2.3.6] 开始执行智能词语对齐算法...`);
                const alignedSegments = this.performIntelligentWordAlignment(
                    this.originalFullSegments, 
                    correctedSegments, 
                    execLogPrefix
                );

                logger.info(`${execLogPrefix}[步骤 2.3.7] 智能对齐完成，最终5字段 segments 数量: ${alignedSegments.length}`);
                return alignedSegments; // 返回包含words字段的完整5字段数据

            } else {
                const errorMsg = `jsonValidator 解析 LLM 返回的 JSON 失败: ${validationResult.error}`;
                logger.error(`${execLogPrefix}[ERROR][步骤 2.3.4] ${errorMsg}`);
                // 解析失败，抛出错误，让上层 (performCorrectionAndExtraction) 处理降级
                throw new Error(errorMsg);
            }

        } catch (parseError) {
            // 捕获解析或校验过程中可能出现的意外错误
            const errorMsg = `解析或校验 LLM 返回的 segments 数据时发生错误: ${parseError.message}`;
            logger.error(`${execLogPrefix}[ERROR][步骤 2.3] ${errorMsg} Stack: ${parseError.stack}`);
            // 抛出错误，让上层 (performCorrectionAndExtraction) 处理降级
            throw new Error(errorMsg);
        }
    }


     /**
      * @功能概述: 验证 segments 数据数组的有效性（增强版 - 强制5字段结构）
      * @param {Array} segments - 要验证的 segments 数组
     * @param {string} execLogPrefix - 执行日志前缀
     * @throws {Error} 当数据无效时抛出错误
     */
     validateSegmentsArray(segments, execLogPrefix) {
         if (!Array.isArray(segments)) {
             const errorMsg = 'segments 数据无效，不是数组类型。';
             logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
             throw new Error(errorMsg);
         }

         if (segments.length === 0) {
              logger.warn(`${execLogPrefix}[WARN] segments 数组为空。`);
             // 根据需求决定空数组是否是错误情况，目前允许空数组通过校验
             return;
         }

         // Token优化后验证4字段结构：id, start, end, text（words字段由智能对齐算法添加）
         const invalidSegments = segments.filter(seg => {
            if (typeof seg !== 'object' || seg === null) {
                return true;
            }

            // 验证必需的4个字段（移除words，因为会通过智能对齐算法添加）
            const requiredFields = ['id', 'start', 'end', 'text'];
            for (const field of requiredFields) {
                if (!(field in seg)) {
                    logger.debug(`${execLogPrefix}[VALIDATION] 缺少必需字段: ${field}`);
                    return true;
                }
            }

            // 验证字段类型
            if (typeof seg.id !== 'string') {
                logger.debug(`${execLogPrefix}[VALIDATION] id 字段必须是字符串类型`);
                return true;
            }
            
            if (typeof seg.start !== 'number') {
                logger.debug(`${execLogPrefix}[VALIDATION] start 字段必须是数字类型`);
                return true;
            }
            
            if (typeof seg.end !== 'number') {
                logger.debug(`${execLogPrefix}[VALIDATION] end 字段必须是数字类型`);
                return true;
            }
            
            if (typeof seg.text !== 'string') {
                logger.debug(`${execLogPrefix}[VALIDATION] text 字段必须是字符串类型`);
                return true;
            }

            return false; // 验证通过
        });

                 if (invalidSegments.length > 0) {
              const errorMsg = `segments 数组中发现 ${invalidSegments.length} 个无效的 segment 对象（不符合4字段标准结构）。`;
              logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
             // 记录一些无效 segment 的预览
             invalidSegments.slice(0, 3).forEach((seg, index) => {
                 const segPreview = {
                     id: seg?.id || 'MISSING',
                     start: seg?.start || 'MISSING',
                     end: seg?.end || 'MISSING',
                     text: seg?.text ? 'PRESENT' : 'MISSING'
                 };
                 logger.debug(`${execLogPrefix}[ERROR] 无效 Segment ${index + 1} 结构: ${JSON.stringify(segPreview)}`);
             });
             throw new Error(errorMsg);
         }
          logger.info(`${execLogPrefix} segments 数组验证通过 - 符合Token优化的4字段结构 (${segments.length} 个条目)`);
     }










     /**
      * @功能概述: 将秒数时间戳转换为 HH:MM:SS,ms 格式
      * @param {number} seconds - 秒数时间戳
      * @returns {string} HH:MM:SS,ms 格式的时间字符串
      */
     formatTime(seconds) {
         if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
              logger.warn(`[WARN] formatTime 输入无效秒数: ${seconds}`);
             return '00:00:00,000'; // 返回默认值或抛出错误
         }
         const date = new Date(seconds * 1000);
         const hours = String(date.getUTCHours()).padStart(2, '0');
         const minutes = String(date.getUTCMinutes()).padStart(2, '0');
         const sec = String(date.getUTCSeconds()).padStart(2, '0');
         const ms = String(date.getUTCMilliseconds()).padStart(3, '0');
         return `${hours}:${minutes}:${sec},${ms}`;
     }


 

     async saveSimplifiedSubtitleJson(simplifiedJsonArray, videoIdentifier, execLogPrefix,savePath) {
        // 生成时间戳确保文件名唯一性
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        // 生成标准化文件名：[视频标识符]_corrected_[时间戳].json
        const filename = `${videoIdentifier}_corrected_${timestamp}.json`;
        
        try {
            // 调用通用文件保存工具（参数说明：数据内容，文件名，基础目录，日志前缀）
            // 修改点 1：使用 fileSaver 的标准参数顺序 (data, fileName, baseDir, logPrefix)
            // 修改点 2：使用配置中的 defaultBaseUploadsPath 替代硬编码路径
            // 修改点 3：移除手动路径拼接，由 fileSaver 处理路径组合
            const savedPath = await fileSaver.saveDataToFile(
                JSON.stringify(simplifiedJsonArray, null, 2), // 格式化JSON
                filename, // 参数2: 纯文件名
                savePath, 
                execLogPrefix // 参数4: 日志前缀（原函数第四个参数）
            );
            
            // 新增有效性检查：确保返回路径符合预期
            if (!savedPath) {
                throw new Error('文件保存工具返回空路径');
            }
            return savedPath;
        } catch (saveError) {
            // 修改点 4：在错误信息中增加完整路径信息便于调试
            const errorMsg = `保存简化字幕 JSON 文件失败: ${saveError.message}，路径：${fileSaver.defaultBaseUploadsPath}/${filename}`;
            logger.error(`${execLogPrefix}[ERROR][步骤 4.2] ${errorMsg}`);
            // 向上层抛出标准化错误
            throw new Error(errorMsg);
        }
    }



    generateEnglishSRT(simplifiedJsonArray, execLogPrefix) {
        logger.info(`${execLogPrefix}[步骤 5.2] 开始从简化 JSON 生成英文字幕SRT。`);
        // 验证输入是否是数组
        if (!Array.isArray(simplifiedJsonArray)) {
            const errorMsg = '生成SRT失败：输入的不是有效的简化字幕 JSON 数组。';
            logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        if (simplifiedJsonArray.length === 0) {
            logger.warn(`${execLogPrefix}[WARN] 简化字幕 JSON 数组为空，将生成空的 SRT 内容。`);
            return ''; // 返回空字符串或根据需求返回带有SRT文件头的字符串
        }

        let srtContent = '';
        simplifiedJsonArray.forEach((item) => {
            // 验证关键字段存在且类型正确 (时间戳现在是数字)
            if (!item || typeof item.id !== 'string' || typeof item.start !== 'number' || typeof item.end !== 'number' || typeof item.text !== 'string') {
                logger.warn(`${execLogPrefix}[WARN][generateEnglishSRT] 发现一个结构无效或时间戳非数字的简化JSON条目: ${JSON.stringify(item).substring(0, 100)}... 跳过生成其SRT块。`);
                return;
            }
            srtContent += `${item.id}\r\n`;
            // 将浮点型秒数转换为 HH:MM:SS,ms 格式
            const startTimeFormatted = this.formatTime(item.start);
            const endTimeFormatted = this.formatTime(item.end);
            srtContent += `${startTimeFormatted} --> ${endTimeFormatted}\r\n`;
            srtContent += `${item.text.trim()}\r\n\r\n`; // 每个块后确保两个CRLF
        });

        // 移除末尾可能多余的一个空行
        if (srtContent.endsWith('\r\n\r\n')) {
             srtContent = srtContent.slice(0, -2);
        } else if (srtContent.endsWith('\r\n')) {
             // 如果只有一个空行，保留，因为最后一个字幕块后需要一个空行
        }

        // 验证生成结果
        if (typeof srtContent !== 'string') { // 理论上不会发生，因为我们构建的是字符串
            const errorMsg = '生成的SRT内容无效，不是字符串类型';
            logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        logger.info(`${execLogPrefix}[步骤 5.3] 英文字幕SRT内容生成完成。长度: ${srtContent.length} 字符`);
        logger.debug(`${execLogPrefix}[步骤 5.4] SRT内容预览 (前200字符): ${srtContent.substring(0, 200)}...`);

        return srtContent;
    }




    async saveEnglishSRT(englishSrtContent, videoIdentifier, execLogPrefix,savePath) {
        // 生成时间戳确保文件名唯一性
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        // 生成标准化文件名：[视频标识符]_corrected_english_subtitle_[时间戳].srt
        const filename = `${videoIdentifier}_corrected_english_subtitle_${timestamp}.srt`;
        
        try {
            // 调用通用文件保存工具（参数说明：数据内容，文件名，基础目录，日志前缀）   
            const savedPath = await fileSaver.saveDataToFile(
                englishSrtContent, // 参数1: 数据内容
                filename,          // 参数2: 纯文件名
                savePath, // 参数3: 使用预定义的上传目录（对应fileSaver.js的baseDirectory参数）
                execLogPrefix      // 参数4: 日志前缀（对应fileSaver.js的callerLogPrefix参数）
            );

            if (!savedPath) {
                throw new Error('文件保存工具返回空路径');
            }
            return savedPath;
        } catch (saveError) {
            // 使用path.join确保跨平台路径兼容性（根据fileSaver.js的实现逻辑）
            const fullPath = path.join(fileSaver.defaultBaseUploadsPath, filename);
            const errorMsg = `保存英文字幕 SRT 文件失败: ${saveError.message}，路径：${fullPath}`;
            logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }
    }


    /**
     * @功能概述: 确保上下文对象中包含所有必需的字段，否则抛出错误。
     * @param {object} context - 要验证的上下文对象。
     * @param {Array<string>} requiredFields - 必需字段名称数组。
     * @param {string} logPrefix - 日志前缀，用于错误日志记录。
     *
     * @throws {Error} 当缺少任何必需字段时抛出 `Error`，错误消息会明确指出缺失的字段名，并包含'执行失败'前缀。
     *
     * @验证逻辑:
     *   - 遍历 `requiredFields` 数组中的每个字段名。
     *   - 检查 `context` 对象中对应字段是否存在且不为 `undefined` 或 `null`。
     *   - 如果发现任何必需字段缺失，立即记录错误日志并抛出 `Error`。
     *   - 所有字段验证通过后，记录一条调试日志表示验证成功。
     *
     * @错误处理: 错误直接抛出，由调用方的 `catch` 块（例如 `execute` 方法）进行统一处理。
     */
    validateRequiredFields(context, requiredFields, logPrefix) {
        // 遍历所有必需字段进行验证
        for (const field of requiredFields) {
            // 检查字段是否存在且不为 undefined 或者 null
            if (context[field] === undefined || context[field] === null) {
                // 构建详细的错误消息，包含缺失的字段名
                const errorMsg = `执行失败：上下文缺少必需字段 "${field}"`;
                // 记录错误日志，使用传递进来的 logPrefix
                logger.error(`${logPrefix}[ERROR] ${errorMsg}`);
                // 标记任务失败
                const validationError = new Error(errorMsg);
                this.fail(validationError);
                // 直接抛出错误，让execute方法的catch块统一处理
                throw validationError;
            }
        }
        // 所有字段验证通过，记录成功日志，使用传递进来的 logPrefix
        logger.debug(`${logPrefix} 输入参数验证通过。`);
    }

    /**
     * @功能概述: 智能词语对齐算法 - 核心方法
     * @param {Array} originalSegments - 原始5字段segments（包含words）
     * @param {Array} correctedSegments - LLM修正后的4字段segments（不包含words）
     * @returns {Array} 包含对齐words的完整5字段segments
     */
    performIntelligentWordAlignment(originalSegments, correctedSegments, execLogPrefix) {
        logger.info(`${execLogPrefix}[智能对齐] 开始执行智能词语对齐算法`);
        
        const alignedSegments = [];
        
        for (let i = 0; i < correctedSegments.length; i++) {
            const correctedSegment = correctedSegments[i];
            const originalSegment = originalSegments.find(seg => seg.id === correctedSegment.id);
            
            if (!originalSegment) {
                logger.warn(`${execLogPrefix}[智能对齐] 未找到ID为 ${correctedSegment.id} 的原始segment，跳过`);
                alignedSegments.push(correctedSegment);
                continue;
            }
            
            // 如果文本相同或仅大小写不同，直接复用原始words
            const originalText = originalSegment.text.toLowerCase();
            const correctedText = correctedSegment.text.toLowerCase();
            
            if (originalText === correctedText) {
                logger.debug(`${execLogPrefix}[智能对齐] Segment ${correctedSegment.id} 文本相同，直接复用words`);
                alignedSegments.push({
                    ...correctedSegment,
                    words: originalSegment.words
                });
                continue;
            }
            
            // 执行编辑距离对齐
            logger.debug(`${execLogPrefix}[智能对齐] Segment ${correctedSegment.id} 需要重新对齐`);
            const alignedWords = this.alignWordsUsingEditDistance(
                originalSegment.words,
                correctedSegment.text,
                correctedSegment.start,
                correctedSegment.end,
                execLogPrefix
            );
            
            alignedSegments.push({
                ...correctedSegment,
                words: alignedWords
            });
        }
        
        logger.info(`${execLogPrefix}[智能对齐] 对齐完成，处理了 ${alignedSegments.length} 个segments`);
        return alignedSegments;
    }

    /**
     * @功能概述: 基于编辑距离算法对齐单个segment的words数组
     */
    alignWordsUsingEditDistance(originalWords, correctedText, segmentStart, segmentEnd, logPrefix) {
        // 提取原始文本和词语
        const originalWordsText = originalWords.map(w => w.text.toLowerCase().trim());
        const correctedWordsText = correctedText.toLowerCase().trim().split(/\s+/).filter(w => w.length > 0);
        
        // 计算编辑距离和对齐路径
        const alignment = this.computeEditDistanceAlignment(originalWordsText, correctedWordsText);
        
        // 基于对齐路径重新分配时间戳
        const alignedWords = this.redistributeTimestamps(
            originalWords,
            correctedWordsText, 
            alignment,
            segmentStart,
            segmentEnd
        );

        return alignedWords;
    }

    /**
     * @功能概述: 计算编辑距离和对齐路径
     */
    computeEditDistanceAlignment(source, target) {
        const m = source.length;
        const n = target.length;
        
        // 动态规划矩阵
        const dp = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));
        const operations = Array(m + 1).fill(null).map(() => Array(n + 1).fill(null));
        
        // 初始化边界条件
        for (let i = 0; i <= m; i++) {
            dp[i][0] = i;
            operations[i][0] = 'DELETE';
        }
        for (let j = 0; j <= n; j++) {
            dp[0][j] = j;
            operations[0][j] = 'INSERT';
        }
        
        // 填充DP矩阵
        for (let i = 1; i <= m; i++) {
            for (let j = 1; j <= n; j++) {
                if (source[i-1] === target[j-1]) {
                    dp[i][j] = dp[i-1][j-1];
                    operations[i][j] = 'MATCH';
                } else {
                    const substituteCost = dp[i-1][j-1] + 1;
                    const deleteCost = dp[i-1][j] + 1;
                    const insertCost = dp[i][j-1] + 1;
                    
                    if (substituteCost <= deleteCost && substituteCost <= insertCost) {
                        dp[i][j] = substituteCost;
                        operations[i][j] = 'SUBSTITUTE';
                    } else if (deleteCost <= insertCost) {
                        dp[i][j] = deleteCost;
                        operations[i][j] = 'DELETE';
                    } else {
                        dp[i][j] = insertCost;
                        operations[i][j] = 'INSERT';
                    }
                }
            }
        }
        
        // 回溯构建对齐路径
        const alignment = [];
        let i = m, j = n;
        
        while (i > 0 || j > 0) {
            const op = operations[i][j];
            
            switch (op) {
                case 'MATCH':
                case 'SUBSTITUTE':
                    alignment.unshift({
                        operation: op,
                        srcIndex: i - 1,
                        tgtIndex: j - 1,
                        srcWord: source[i - 1],
                        tgtWord: target[j - 1]
                    });
                    i--; j--;
                    break;
                case 'DELETE':
                    alignment.unshift({
                        operation: op,
                        srcIndex: i - 1,
                        tgtIndex: -1,
                        srcWord: source[i - 1],
                        tgtWord: null
                    });
                    i--;
                    break;
                case 'INSERT':
                    alignment.unshift({
                        operation: op,
                        srcIndex: -1,
                        tgtIndex: j - 1,
                        srcWord: null,
                        tgtWord: target[j - 1]
                    });
                    j--;
                    break;
            }
        }
        
        return alignment;
    }

    /**
     * @功能概述: 基于对齐路径重新分配时间戳
     */
    redistributeTimestamps(originalWords, correctedWordsText, alignment, segmentStart, segmentEnd) {
        const result = [];
        const segmentDuration = segmentEnd - segmentStart;
        let currentTime = segmentStart;
        
        const targetWordCount = correctedWordsText.length;
        const avgWordDuration = targetWordCount > 0 ? segmentDuration / targetWordCount : 0;
        
        for (const align of alignment) {
            if (align.operation === 'DELETE') {
                continue;
            }
            
            if (align.operation === 'MATCH') {
                const originalWord = originalWords[align.srcIndex];
                result.push({
                    text: align.tgtWord,
                    start: originalWord.start,
                    end: originalWord.end
                });
                currentTime = originalWord.end;
            } else if (align.operation === 'SUBSTITUTE') {
                const originalWord = originalWords[align.srcIndex];
                result.push({
                    text: align.tgtWord,
                    start: originalWord.start,
                    end: originalWord.end
                });
                currentTime = originalWord.end;
            } else if (align.operation === 'INSERT') {
                const wordEnd = currentTime + avgWordDuration;
                result.push({
                    text: align.tgtWord,
                    start: currentTime,
                    end: Math.min(wordEnd, segmentEnd)
                });
                currentTime = Math.min(wordEnd, segmentEnd);
            }
        }
        
        if (result.length > 0) {
            result[result.length - 1].end = Math.min(result[result.length - 1].end, segmentEnd);
        }
        
        return result;
    }

    /**
     * @功能概述: 收集TranscriptionCorrectionTask的详细上下文信息
     * @returns {object} 包含转录校正特定信息的详细上下文
     *
     * @说明:
     *   - 覆盖父类的collectDetailedContext方法
     *   - 添加转录校正特定的上下文信息
     *   - 包含LLM处理详情、校正参数、校正历史等
     *   - 保持与现有逻辑的完全兼容性
     *
     * @返回对象扩展:
     *   - transcriptionProcessingDetails: 转录处理特定信息
     *   - llmDetails: LLM交互详情
     *   - inputTranscriptionInfo: 输入转录信息
     *   - outputTranscriptionInfo: 输出转录信息
     *   - correctionParameters: 校正参数详情
     *   - correctionHistory: 校正历史
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息（继承自TaskBase）
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取转录校正信息
            const taskResult = this.result || {};

            // 扩展输入上下文信息（覆盖基类的基础结构）
            const inputContext = {
                ...baseContext.inputContext,
                apiResponseReceived: taskResult.apiResponse ? true : false,
                segmentsCount: taskResult.extractedRawJsonData?.segments?.length || 'N/A',
                originalLanguage: taskResult.extractedRawJsonData?.language || 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                reqId: taskResult.reqId || 'N/A',
                inputFormat: 'azure_speech_api_response',
                inputSource: 'speech_recognition_system'
            };

            // 扩展输出上下文信息（覆盖基类的基础结构）
            const outputContext = {
                ...baseContext.outputContext,
                correctedFullText: taskResult.correctedFullText || 'N/A',
                correctedTextLength: taskResult.correctedFullText ? taskResult.correctedFullText.length : 0,
                simplifiedSubtitleJsonPath: taskResult.simplifiedSubtitleJsonPath || 'N/A',
                englishSrtPath: taskResult.englishSrtPath || 'N/A',
                englishSrtContent: taskResult.englishSrtContent ? 'generated' : 'N/A',
                correctionQuality: 'llm_enhanced_with_fallback',
                outputFormat: 'multiple_formats',
                processingSuccess: taskResult.correctionStatus === 'success'
            };

            // 扩展技术细节信息（覆盖基类的基础结构）
            const technicalDetails = {
                ...baseContext.technicalDetails,
                taskType: 'TranscriptionCorrection',
                llmProvider: 'Google Gemini',
                llmModel: 'google/gemini-2.5-flash-preview-05-20',
                supportedInputFormats: ['apiResponse', 'segments_array'],
                outputFormat: 'corrected_transcription',
                processingMode: 'llm_correction_with_fallback',
                timeout: 60000,
                timeoutManagement: 'request_timeout',
                jsonValidationEnabled: true,
                fileSavingEnabled: true,
                fallbackEnabled: true
            };

            // 转录处理特定信息
            const transcriptionProcessingDetails = {
                processingSteps: [
                    '参数验证',
                    'LLM校正处理',
                    '数据提取校验',
                    '简化字幕转换',
                    'SRT生成',
                    '文件保存'
                ],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '文件保存' :
                           this.status === TASK_STATUS.FAILED ? '错误处理' : '执行中',
                stepProgress: this.status === TASK_STATUS.COMPLETED ? '6/6' :
                            this.status === TASK_STATUS.FAILED ? 'N/A' : 'N/A',
                processingMethod: 'llm_correction_with_fallback',
                qualityLevel: 'high',
                correctionStrategy: 'context_aware_correction',
                fallbackStrategy: 'use_original_data'
            };

            // LLM交互详情（任务特定）
            const llmDetails = {
                provider: 'Google Gemini',
                model: 'google/gemini-2.5-flash-preview-05-20',
                endpoint: 'openrouter_api',
                requestMethod: 'POST',
                responseFormat: 'json',
                errorHandling: 'comprehensive_with_fallback',
                retryStrategy: 'enabled',
                promptTemplate: 'CORRECT_TRANSCRIPTION.default',
                fallbackStrategy: 'use_original_data',
                connectionStatus: this.status === TASK_STATUS.COMPLETED ? 'success' :
                               this.status === TASK_STATUS.FAILED ? 'failed' : 'unknown'
            };

            // 输入转录信息（任务特定）
            const inputTranscriptionInfo = {
                apiResponseReceived: taskResult.apiResponse ? true : false,
                segmentsCount: taskResult.extractedRawJsonData?.segments?.length || 'N/A',
                originalLanguage: taskResult.extractedRawJsonData?.language || 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                inputFormat: 'azure_speech_api_response',
                inputSource: 'speech_recognition_system',
                dataValidation: 'json_validator_enabled'
            };

            // 输出转录信息（任务特定）
            const outputTranscriptionInfo = {
                correctedFullText: taskResult.correctedFullText || 'N/A',
                correctedTextLength: taskResult.correctedFullText ? taskResult.correctedFullText.length : 0,
                simplifiedSubtitleJsonPath: taskResult.simplifiedSubtitleJsonPath || 'N/A',
                englishSrtPath: taskResult.englishSrtPath || 'N/A',
                englishSrtContent: taskResult.englishSrtContent ? 'generated' : 'N/A',
                correctionQuality: 'llm_enhanced_with_fallback',
                outputFormat: 'multiple_formats',
                processingSuccess: taskResult.correctionStatus === 'success',
                filesGenerated: [
                    taskResult.simplifiedSubtitleJsonPath ? 'simplified_subtitle.json' : null,
                    taskResult.englishSrtPath ? 'english_subtitle.srt' : null
                ].filter(Boolean)
            };

            // 校正参数详情（任务特定）
            const correctionParameters = {
                reqId: taskResult.reqId || 'N/A',
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                savePath: taskResult.savePath || 'N/A',
                correctionMethod: 'llm_with_original_fallback',
                promptStrategy: 'context_aware_correction',
                responseValidation: 'json_validator_enabled',
                fallbackEnabled: true,
                llmTimeout: 60000
            };

            // 校正历史详情（任务特定）
            const correctionHistory = {
                correctionType: 'llm_based_with_fallback',
                inputFormat: 'azure_api_response',
                outputFormat: 'multiple_corrected_formats',
                correctionMethod: 'google_gemini',
                processingTime: this.getElapsedTime(),
                correctionSuccess: this.status === TASK_STATUS.COMPLETED,
                errorOccurred: this.status === TASK_STATUS.FAILED,
                lastError: this.error ? {
                    message: this.error.message,
                    name: this.error.name,
                    type: this.error.constructor.name
                } : null,
                progressUpdatesCount: this.progressHistory.length,
                lastProgressUpdate: this.progressHistory.length > 0 ?
                    this.progressHistory[this.progressHistory.length - 1] : null
            };

            // 合并所有上下文信息（遵循TaskBase标准结构）
            const extendedContext = {
                // 基础信息（来自TaskBase）
                taskInfo: baseContext.taskInfo,
                executionStats: baseContext.executionStats,
                progressHistory: baseContext.progressHistory,

                // 扩展的上下文信息（覆盖基类默认值）
                inputContext,
                outputContext,
                technicalDetails,

                // 任务特定的详细信息
                transcriptionProcessingDetails,
                llmDetails,
                inputTranscriptionInfo,
                outputTranscriptionInfo,
                correctionParameters,
                correctionHistory,

                // 元信息
                collectedAt: new Date().toISOString(),
                collectionMethod: 'TranscriptionCorrectionTask.collectDetailedContext'
            };

            logger.info(`${logPrefix} 成功收集TranscriptionCorrectionTask详细上下文信息`);
            logger.info(`${logPrefix} 上下文包含 ${Object.keys(extendedContext).length} 个主要部分`);

            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                transcriptionProcessingError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString(),
                collectionMethod: 'TranscriptionCorrectionTask.collectDetailedContext (with error)'
            };
        }
    }
}

// 导出TranscriptionCorrectionTask类，供其他模块使用
module.exports = TranscriptionCorrectionTask;

// 记录模块导出完成的日志
logger.info(`${taskModuleLogPrefix}TranscriptionCorrectionTask 类已导出。`); 