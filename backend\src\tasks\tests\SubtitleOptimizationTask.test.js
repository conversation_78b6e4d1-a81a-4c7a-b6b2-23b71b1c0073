// 导入必要的模块
const fs = require('fs');
const path = require('path');
const SubtitleOptimizationTask = require('../SubtitleOptimizationTask');
const logger = require('../../utils/logger');

// 测试配置
const TEST_CONFIG = {
    apiJsonPath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output\\videoFile-1749952703908-294201027_transcription.json',
    savePath: 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output',
    reqId: `test_req_${Date.now()}`,
    videoIdentifier: `test_video_${Math.random().toString(36).substring(7)}`
};

/**
 * @功能概述: SubtitleOptimizationTask 测试套件
 */
class SubtitleOptimizationTaskTest {
    constructor() {
        this.testResults = [];
        this.testLogPrefix = '[SubtitleOptimizationTask.test.js]';
    }

    /**
     * @功能概述: 运行所有测试
     */
    async runAllTests() {
        logger.info(`${this.testLogPrefix} 开始运行 SubtitleOptimizationTask 测试套件`);
        logger.info(`${this.testLogPrefix} 测试配置: ${JSON.stringify(TEST_CONFIG, null, 2)}`);

        const tests = [
            'testTaskInstantiation',
            'testInputValidation', 
            'testSubtitleOptimizationWithRealData',
            'testMergeShortSegments',
            'testProgressReporting',
            'testSimplifiedJsonConversion'
        ];

        let passedTests = 0;
        let totalTests = tests.length;

        for (const testName of tests) {
            try {
                logger.info(`${this.testLogPrefix} 运行测试: ${testName}`);
                if (testName === 'testTaskInstantiation') {
                    await this.testTaskInstantiation();
                } else if (testName === 'testInputValidation') {
                    await this.testInputValidation();
                } else if (testName === 'testSubtitleOptimizationWithRealData') {
                    await this.testSubtitleOptimizationWithRealData();
                } else if (testName === 'testMergeShortSegments') {
                    await this.testMergeShortSegments();
                } else if (testName === 'testProgressReporting') {
                    await this.testProgressReporting();
                } else if (testName === 'testSimplifiedJsonConversion') {
                    await this.testSimplifiedJsonConversion();
                } else {
                    logger.warn(`[SubtitleOptimizationTask.test.js] 未知测试: ${testName}`);
                }
                this.logTestResult(testName, true);
                passedTests++;
            } catch (error) {
                this.logTestResult(testName, false, error.message);
                logger.error(`${this.testLogPrefix} 测试失败 ${testName}: ${error.message}`);
                logger.error(`${this.testLogPrefix} 错误堆栈: ${error.stack}`);
            }
        }

        // 输出测试摘要
        logger.info(`${this.testLogPrefix} 测试完成。通过: ${passedTests}/${totalTests}`);
        this.printTestSummary();

        return { passedTests, totalTests, success: passedTests === totalTests };
    }

    /**
     * @功能概述: 测试任务实例化
     */
    async testTaskInstantiation() {
        const task = new SubtitleOptimizationTask();
        
        if (!task || task.constructor.name !== 'SubtitleOptimizationTask') {
            throw new Error('任务实例化失败');
        }

        if (typeof task.execute !== 'function') {
            throw new Error('execute 方法不存在');
        }

        // 测试自定义配置
        const customTask = new SubtitleOptimizationTask('CustomOptimization', {
            minWordCount: 3,
            maxGapSeconds: 1.0,
            mergeDirection: 'backward'
        });

        if (customTask.options.minWordCount !== 3) {
            throw new Error('自定义配置未正确应用');
        }

        logger.info(`${this.testLogPrefix} ✓ 任务实例化测试通过`);
    }

    /**
     * @功能概述: 测试输入参数验证
     */
    async testInputValidation() {
        const task = new SubtitleOptimizationTask();

        // 测试缺少必需字段
        const invalidContexts = [
            {}, // 完全空的上下文
            { reqId: 'test' }, // 缺少其他字段
            { reqId: 'test', videoIdentifier: 'vid', apiResponse: null }, // apiResponse 为 null
            { reqId: 'test', videoIdentifier: 'vid', apiResponse: {}, savePath: 'path' }, // apiResponse 缺少 segments
            { reqId: 'test', videoIdentifier: 'vid', apiResponse: { segments: 'not_array' }, savePath: 'path' } // segments 不是数组
        ];

        for (const invalidContext of invalidContexts) {
            try {
                await task.execute(invalidContext, () => {});
                throw new Error(`应该抛出验证错误，但没有抛出。上下文: ${JSON.stringify(invalidContext)}`);
            } catch (error) {
                if (!error.message.includes('执行失败：上下文缺少必需') && !error.message.includes('segments数组')) {
                    throw new Error(`意外的错误类型: ${error.message}`);
                }
            }
        }

        logger.info(`${this.testLogPrefix} ✓ 输入参数验证测试通过`);
    }

    /**
     * @功能概述: 使用真实数据测试字幕优化功能
     */
    async testSubtitleOptimizationWithRealData() {
        // 读取API响应数据
        const apiResponse = this.loadApiResponseData();
        
        // 验证数据格式
        if (!apiResponse.segments || !Array.isArray(apiResponse.segments)) {
            throw new Error('API响应数据格式不正确，缺少segments数组');
        }

        logger.info(`${this.testLogPrefix} 加载的数据包含 ${apiResponse.segments.length} 个segments`);

        // 创建任务实例
        const task = new SubtitleOptimizationTask('RealDataTest', {
            minWordCount: 2,
            maxGapSeconds: 0.5,
            mergeDirection: 'forward'
        });

        // 构建测试上下文
        const context = {
            reqId: TEST_CONFIG.reqId,
            videoIdentifier: TEST_CONFIG.videoIdentifier,
            apiResponse: apiResponse,
            savePath: TEST_CONFIG.savePath
        };

        // 执行优化
        const progressCallback = (progress) => {
            logger.debug(`${this.testLogPrefix} 进度更新: ${JSON.stringify(progress)}`);
        };

        const result = await task.execute(context, progressCallback);

        // 验证结果
        this.validateOptimizationResult(result, apiResponse);

        logger.info(`${this.testLogPrefix} ✓ 真实数据字幕优化测试通过`);
        logger.info(`${this.testLogPrefix} 优化效果: ${result.originalSegmentsCount} -> ${result.optimizedSegmentsCount} segments`);
    }

    /**
     * @功能概述: 测试合并短segments的逻辑
     */
    async testMergeShortSegments() {
        const task = new SubtitleOptimizationTask('MergeTest', {
            minWordCount: 2,
            maxGapSeconds: 0.5
        });

        // 创建测试segments（包含需要合并的短segments）
        const testSegments = [
            {
                id: 0,
                seek: 0,
                start: 0.0,
                end: 1.0,
                text: "Hello world this is a longer sentence",
                tokens: [],
                temperature: 0,
                avg_logprob: -0.1,
                compression_ratio: 1.5,
                no_speech_prob: 0.02,
                words: [
                    { text: "Hello", start: 0.0, end: 0.2 },
                    { text: "world", start: 0.2, end: 0.5 }
                ]
            },
            {
                id: 1,
                seek: 0,
                start: 1.1,
                end: 1.3,
                text: "U.S.", // 短segment，需要合并
                tokens: [],
                temperature: 0,
                avg_logprob: -0.1,
                compression_ratio: 1.0,
                no_speech_prob: 0.03,
                words: [
                    { text: "U.S.", start: 1.1, end: 1.3 }
                ]
            },
            {
                id: 2,
                seek: 0,
                start: 1.4,
                end: 3.0,
                text: "President Donald Trump is speaking",
                tokens: [],
                temperature: 0,
                avg_logprob: -0.1,
                compression_ratio: 1.8,
                no_speech_prob: 0.02,
                words: [
                    { text: "President", start: 1.4, end: 1.7 },
                    { text: "Donald", start: 1.7, end: 2.0 }
                ]
            }
        ];

        const mergedSegments = task.mergeShortSegments(testSegments, `${this.testLogPrefix}[MergeTest]`);

        // 验证合并结果
        if (mergedSegments.length >= testSegments.length) {
            throw new Error('短segments未被合并');
        }

        // 检查U.S.是否被合并到后面的segment中
        const foundMergedText = mergedSegments.some(seg => 
            seg.text.includes('U.S.') && seg.text.includes('President')
        );

        if (!foundMergedText) {
            throw new Error('U.S. 未正确合并到下一个segment');
        }

        // 验证ID重新分配
        for (let i = 0; i < mergedSegments.length; i++) {
            if (mergedSegments[i].id !== i) {
                throw new Error(`ID重新分配失败，segment[${i}].id = ${mergedSegments[i].id}`);
            }
        }

        logger.info(`${this.testLogPrefix} ✓ 合并短segments逻辑测试通过`);
        logger.info(`${this.testLogPrefix} 合并效果: ${testSegments.length} -> ${mergedSegments.length} segments`);
    }

    /**
     * @功能概述: 测试进度报告功能
     */
    async testProgressReporting() {
        const task = new SubtitleOptimizationTask();
        const progressUpdates = [];

        const progressCallback = (progress) => {
            progressUpdates.push(progress);
            // 添加调试信息
            logger.debug(`${this.testLogPrefix}[进度调试] 收到进度: 状态=${progress.status}, 子状态=${progress.subStatus}, 详情=${progress.detail}`);
        };

        // 创建简单的测试数据
        const testData = {
            segments: [
                { id: 0, text: "Hello", start: 0, end: 1, tokens: [], temperature: 0, avg_logprob: -0.1, compression_ratio: 1.0, no_speech_prob: 0.02, words: [] },
                { id: 1, text: "Hi", start: 1.1, end: 1.5, tokens: [], temperature: 0, avg_logprob: -0.1, compression_ratio: 1.0, no_speech_prob: 0.02, words: [] }
            ]
        };

        const context = {
            reqId: 'progress_test',
            videoIdentifier: 'progress_video',
            apiResponse: testData,
            savePath: TEST_CONFIG.savePath
        };

        await task.execute(context, progressCallback);

        // 验证进度更新
        if (progressUpdates.length === 0) {
            throw new Error('没有收到进度更新');
        }

        // 输出所有进度更新用于调试
        logger.info(`${this.testLogPrefix}[调试] 收到的所有进度更新:`);
        progressUpdates.forEach((progress, index) => {
            logger.info(`${this.testLogPrefix}[调试] ${index + 1}. 状态=${progress.status}, 子状态=${progress.subStatus}, 详情=${progress.detail}`);
        });

        // 简化测试 - 检查是否有足够的进度更新阶段
        const hasStarted = progressUpdates.some(p => p.status === 'started');
        const hasProcessing = progressUpdates.filter(p => p.subStatus === 'processing').length >= 3; // 应该有至少3个processing阶段
        const hasCompleted = progressUpdates.some(p => p.status === 'completed');

        logger.info(`${this.testLogPrefix}[调试] 简化阶段检查结果:`);
        logger.info(`${this.testLogPrefix}[调试] - hasStarted: ${hasStarted}`);
        logger.info(`${this.testLogPrefix}[调试] - hasProcessing (>=3): ${hasProcessing}`);
        logger.info(`${this.testLogPrefix}[调试] - hasCompleted: ${hasCompleted}`);
        logger.info(`${this.testLogPrefix}[调试] - 总进度更新数: ${progressUpdates.length}`);

        if (!hasStarted) {
            throw new Error('进度报告缺少开始状态');
        }
        if (!hasProcessing) {
            throw new Error('进度报告缺少足够的处理阶段（需要至少3个processing阶段）');
        }
        if (!hasCompleted) {
            throw new Error('进度报告缺少完成状态');
        }

        logger.info(`${this.testLogPrefix} ✓ 进度报告功能测试通过，共收到 ${progressUpdates.length} 次进度更新`);
    }

    /**
     * @功能概述: 测试简化字幕JSON转换功能
     */
    async testSimplifiedJsonConversion() {
        // 读取API响应数据
        const apiResponse = this.loadApiResponseData();
        
        logger.info(`${this.testLogPrefix} 开始简化JSON转换测试，原始segments: ${apiResponse.segments.length}`);

        // 创建任务实例
        const task = new SubtitleOptimizationTask('SimplifiedJsonTest', {
            minWordCount: 2,
            maxGapSeconds: 0.5,
            mergeDirection: 'forward'
        });

        // 构建测试上下文
        const context = {
            reqId: `simplified_test_${Date.now()}`,
            videoIdentifier: `test_simplified_${Math.random().toString(36).substring(7)}`,
            apiResponse: apiResponse,
            savePath: TEST_CONFIG.savePath
        };

        // 执行优化（包含简化JSON转换）
        const result = await task.execute(context);

        // 验证基本结果
        if (!result || result.optimizationStatus !== 'success') {
            throw new Error('简化JSON转换测试失败 - 任务未成功完成');
        }

        // 验证新增的简化JSON字段
        if (!result.simplifiedSubtitleJsonArray || !Array.isArray(result.simplifiedSubtitleJsonArray)) {
            throw new Error('简化JSON转换测试失败 - 缺少simplifiedSubtitleJsonArray');
        }

        if (!result.simplifiedSubtitleJsonPath || typeof result.simplifiedSubtitleJsonPath !== 'string') {
            throw new Error('简化JSON转换测试失败 - 缺少simplifiedSubtitleJsonPath');
        }

        const simplifiedArray = result.simplifiedSubtitleJsonArray;
        
        if (simplifiedArray.length === 0) {
            throw new Error('简化JSON转换测试失败 - 简化JSON数组为空');
        }

        logger.info(`${this.testLogPrefix} 简化JSON转换结果: ${result.originalSegmentsCount} -> ${result.optimizedSegmentsCount} -> ${simplifiedArray.length} 字幕块`);

        // 验证每个简化字幕对象的结构（检查前5个）
        const samplesToCheck = Math.min(5, simplifiedArray.length);
        for (let i = 0; i < samplesToCheck; i++) {
            const item = simplifiedArray[i];
            
            // 验证必需的5个字段
            const requiredFields = ['id', 'start', 'end', 'text', 'words'];
            for (const field of requiredFields) {
                if (!(field in item)) {
                    throw new Error(`简化JSON转换测试失败 - 第${i}个对象缺少字段: ${field}`);
                }
            }
            
            // 验证字段类型和值
            if (typeof item.id !== 'string' || item.id.trim() === '') {
                throw new Error(`简化JSON转换测试失败 - 第${i}个对象id字段无效: ${item.id}`);
            }
            
            if (typeof item.start !== 'number' || typeof item.end !== 'number') {
                throw new Error(`简化JSON转换测试失败 - 第${i}个对象时间戳字段类型无效`);
            }
            
            if (item.start >= item.end) {
                throw new Error(`简化JSON转换测试失败 - 第${i}个对象时间戳逻辑错误: start(${item.start}) >= end(${item.end})`);
            }
            
            if (typeof item.text !== 'string') {
                throw new Error(`简化JSON转换测试失败 - 第${i}个对象text字段类型无效`);
            }
            
            if (!Array.isArray(item.words)) {
                throw new Error(`简化JSON转换测试失败 - 第${i}个对象words字段不是数组`);
            }
            
            // 验证words数组的结构（如果非空）
            if (item.words.length > 0) {
                const firstWord = item.words[0];
                const wordRequiredFields = ['text', 'start', 'end'];
                for (const field of wordRequiredFields) {
                    if (!(field in firstWord)) {
                        throw new Error(`简化JSON转换测试失败 - 第${i}个对象words[0]缺少字段: ${field}`);
                    }
                }
                
                if (typeof firstWord.text !== 'string' || firstWord.text.trim() === '') {
                    throw new Error(`简化JSON转换测试失败 - 第${i}个对象words[0].text无效`);
                }
                
                if (typeof firstWord.start !== 'number' || typeof firstWord.end !== 'number') {
                    throw new Error(`简化JSON转换测试失败 - 第${i}个对象words[0]时间戳类型无效`);
                }
            }
            
            logger.debug(`${this.testLogPrefix} 验证第${i}个简化对象通过: ID="${item.id}", text="${item.text.substring(0, 30)}...", words数量=${item.words.length}`);
        }
        
        // 验证ID连续性（应该从"1"开始）
        if (simplifiedArray[0].id !== '1') {
            throw new Error(`简化JSON转换测试失败 - 第一个ID应为"1"，实际为"${simplifiedArray[0].id}"`);
        }
        
        if (simplifiedArray.length > 1 && simplifiedArray[1].id !== '2') {
            throw new Error(`简化JSON转换测试失败 - 第二个ID应为"2"，实际为"${simplifiedArray[1].id}"`);
        }
        
        // 验证时间顺序
        for (let i = 1; i < simplifiedArray.length; i++) {
            if (simplifiedArray[i].start < simplifiedArray[i-1].start) {
                throw new Error(`简化JSON转换测试失败 - 时间顺序错误: 第${i}个对象start(${simplifiedArray[i].start}) < 第${i-1}个对象start(${simplifiedArray[i-1].start})`);
            }
        }
        
        // 验证保存的文件存在
        if (!fs.existsSync(result.simplifiedSubtitleJsonPath)) {
            throw new Error(`简化JSON转换测试失败 - 简化JSON文件未正确保存: ${result.simplifiedSubtitleJsonPath}`);
        }
        
        // 验证保存的文件内容
        try {
            const savedContent = fs.readFileSync(result.simplifiedSubtitleJsonPath, 'utf8');
            const parsedContent = JSON.parse(savedContent);
            
            if (!Array.isArray(parsedContent) || parsedContent.length !== simplifiedArray.length) {
                throw new Error('简化JSON转换测试失败 - 保存的文件内容与返回的数组不匹配');
            }
        } catch (fileError) {
            throw new Error(`简化JSON转换测试失败 - 无法读取或解析保存的文件: ${fileError.message}`);
        }
        
        logger.info(`${this.testLogPrefix} ✓ 简化JSON转换功能测试通过`);
        logger.info(`${this.testLogPrefix} ✓ 验证通过: ${simplifiedArray.length} 个字幕块, 文件路径: ${result.simplifiedSubtitleJsonPath}`);
    }

    /**
     * @功能概述: 加载API响应数据
     */
    loadApiResponseData() {
        try {
            if (!fs.existsSync(TEST_CONFIG.apiJsonPath)) {
                throw new Error(`API数据文件不存在: ${TEST_CONFIG.apiJsonPath}`);
            }

            const fileContent = fs.readFileSync(TEST_CONFIG.apiJsonPath, 'utf8');
            const apiResponse = JSON.parse(fileContent);

            logger.info(`${this.testLogPrefix} 成功加载API响应数据，文件大小: ${fileContent.length} 字符`);
            return apiResponse;

        } catch (error) {
            throw new Error(`加载API响应数据失败: ${error.message}`);
        }
    }

    /**
     * @功能概述: 验证优化结果
     */
    validateOptimizationResult(result, originalApiResponse) {
        // 验证基本结构
        const requiredFields = [
            'optimizationStatus', 'originalSegmentsCount', 'optimizedSegmentsCount',
            'optimizedData', 'optimizedFilePath', 'optimizationSteps'
        ];

        for (const field of requiredFields) {
            if (!(field in result)) {
                throw new Error(`结果缺少必需字段: ${field}`);
            }
        }

        // 验证状态
        if (result.optimizationStatus !== 'success') {
            throw new Error(`优化状态不是success: ${result.optimizationStatus}`);
        }

        // 验证segments数量
        if (result.originalSegmentsCount !== originalApiResponse.segments.length) {
            throw new Error(`原始segments数量不匹配: 期望 ${originalApiResponse.segments.length}, 实际 ${result.originalSegmentsCount}`);
        }

        // 验证优化数据结构 - optimizedData是segments数组
        if (!Array.isArray(result.optimizedData)) {
            throw new Error('优化数据应该是segments数组');
        }

        if (result.optimizedData.length !== result.optimizedSegmentsCount) {
            throw new Error('优化后segments数量与报告不一致');
        }

        // 验证文件是否保存
        if (!fs.existsSync(result.optimizedFilePath)) {
            throw new Error(`优化后的文件未保存: ${result.optimizedFilePath}`);
        }

        // 验证ID连续性
        const segments = result.optimizedData;
        for (let i = 0; i < segments.length; i++) {
            if (segments[i].id !== i) {
                throw new Error(`segment ID不连续: segment[${i}].id = ${segments[i].id}`);
            }
        }

        logger.info(`${this.testLogPrefix} ✓ 优化结果验证通过`);
    }

    /**
     * @功能概述: 记录测试结果
     */
    logTestResult(testName, passed, errorMessage = null) {
        const result = {
            testName,
            passed,
            errorMessage,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);
    }

    /**
     * @功能概述: 打印测试摘要
     */
    printTestSummary() {
        logger.info(`${this.testLogPrefix} ========== 测试摘要 ==========`);
        this.testResults.forEach(result => {
            const status = result.passed ? '✓ PASS' : '✗ FAIL';
            const message = result.errorMessage ? ` - ${result.errorMessage}` : '';
            logger.info(`${this.testLogPrefix} ${status}: ${result.testName}${message}`);
        });
        logger.info(`${this.testLogPrefix} ==============================`);
    }
}

// 运行测试
async function runTests() {
    const testRunner = new SubtitleOptimizationTaskTest();
    try {
        const results = await testRunner.runAllTests();
        
        if (results.success) {
            logger.info(`${testRunner.testLogPrefix} 🎉 所有测试通过！`);
            process.exit(0);
        } else {
            logger.error(`${testRunner.testLogPrefix} ❌ 部分测试失败`);
            process.exit(1);
        }
    } catch (error) {
        logger.error(`${testRunner.testLogPrefix} 测试运行器发生错误: ${error.message}`);
        logger.error(`${testRunner.testLogPrefix} 错误堆栈: ${error.stack}`);
        process.exit(1);
    }
}

// 如果直接运行这个文件，则执行测试
if (require.main === module) {
    runTests();
}

module.exports = SubtitleOptimizationTaskTest; 