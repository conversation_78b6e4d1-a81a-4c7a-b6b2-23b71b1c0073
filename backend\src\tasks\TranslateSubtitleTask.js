const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const llmService = require('../services/llmService');
// const promptTemplates = require('../services/promptTemplates'); // 不再直接使用旧的SRT翻译提示词
const { extractAndParseJson } = require('../utils/jsonValidator'); // 导入JSON校验工具
const fileSaver = require('../utils/fileSaver'); // 导入文件保存工具
const path = require('path');
const config = require('../config'); // 修正：移除括号 ()，直接导入已初始化的配置对象
const { TASK_STATUS, TASK_SUBSTATUS } = require('../constants/progress');



// 模块级日志前缀
const taskModuleLogPrefix = '[文件：TranslateSubtitleTask.js][字幕JSON翻译任务][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`);

class TranslateSubtitleTask extends TaskBase {
    /**
     * @功能概述: 构造函数，初始化字幕JSON翻译任务实例
     * @param {string} [name='TranslateSubtitleTask'] - 任务的名称
     */
    constructor(name = 'TranslateSubtitleTask') {
        super(name);
        this.instanceLogPrefix = `[文件：TranslateSubtitleTask.js][字幕JSON翻译任务][${this.name}]`;
        logger.info(`${this.instanceLogPrefix} 实例已创建。`);
    }




    async execute(context, progressCallback) {
        // 步骤 1: 优先从上下文中解构出核心参数，确保它们在后续操作中可用。
        // 这样也使得 execLogPrefix 可以安全地使用 videoIdentifier。
        const { videoIdentifier, simplifiedSubtitleJsonArray, correctedFullText, savePath } = context;

        // 步骤 2: 定义任务执行所必需的字段列表。
        // validateRequiredFields 方法会检查 context 对象是否直接包含这些字段。
        // 注意：correctedFullText 是非必须的，但会传递给LLM作为上下文参考
        const requiredFields = ['videoIdentifier', 'simplifiedSubtitleJsonArray', 'savePath'];

        // 步骤 3: 构建执行日志前缀。
        // 使用解构后的 videoIdentifier，如果未定义则提供默认值以避免日志前缀出错。
        const execLogPrefix = `[文件：TranslateSubtitleTask.js][字幕JSON翻译任务][${videoIdentifier || 'unknown_video'}]`;

        // 步骤 4: 验证必需的上下文参数。
        // 此方法将检查 context 对象是否包含 requiredFields 中列出的所有键。
        this.validateRequiredFields(context, requiredFields, execLogPrefix);

        // 记录任务开始执行的日志
        logger.info(`${execLogPrefix} 开始执行字幕翻译任务`);

        // 设置进度回调函数，以便在任务执行过程中报告进度
        this.setProgressCallback(progressCallback);

        // 调用父类方法，标记任务开始，并记录开始时间
        this.start();

        // [初始化]: 声明并初始化所有任务处理结果相关的变量，这些变量将构成函数的最终输出上下文。
        let translatedSubtitleJsonArray = null; // LLM翻译后的字幕JSON数组
        let translatedSubtitleJsonPath = null; // 翻译后的字幕JSON文件路径
        let chineseSrtContent = null; // 中文字幕SRT内容
        let chineseSrtPath = null; // 中文字幕SRT文件路径

        try {

            // 记录上下文参数验证通过的日志，并显示字幕条目数量
            logger.debug(`${execLogPrefix} 上下文参数验证通过，字幕条目数：${simplifiedSubtitleJsonArray.length}`);

            // 阶段1: 任务初始化
            // 报告任务状态为运行中，子状态为初始化，并更新进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.INITIALIZING, {
                detail: `开始翻译字幕任务 [${videoIdentifier}]`, // 详细描述
                current: 5, // 当前进度百分比
                total: 100 // 总进度百分比
            });
            // 记录任务初始化完成的日志
            logger.info(`${execLogPrefix}[阶段1/4] 任务初始化完成`);

            // 阶段2: LLM翻译处理
            // 记录开始调用LLM翻译服务的日志
            logger.info(`${execLogPrefix}[阶段2/4] 开始调用LLM翻译服务`);
            // 调用LLM服务进行字幕JSON翻译，并等待结果
            translatedSubtitleJsonArray = await this.performLLMJsonTranslation(
                simplifiedSubtitleJsonArray, // 原始简化字幕JSON数组
                correctedFullText,           // 校正后的完整文本作为上下文
                videoIdentifier,             // 视频标识符
                execLogPrefix                // 日志前缀
            );
            // 记录LLM翻译完成的日志，并显示翻译结果的条目数量
            logger.debug(`${execLogPrefix} LLM翻译完成，获得${translatedSubtitleJsonArray.length}条翻译结果`);

            // 阶段3: 保存翻译结果
            // 报告任务状态为运行中，子状态为保存翻译后JSON，并更新进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.SAVING_TRANSLATED_JSON, {
                detail: '保存翻译后JSON文件',
                current: 70,
                total: 100
            });

            // 生成时间戳确保文件名唯一性
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

            // [初始化]: 构建翻译后JSON文件的文件名（使用时间戳避免覆盖）
            const translatedSubtitleJsonName = `${videoIdentifier}_translated_chinese_subtitle_${timestamp}.json`;

            // 记录开始保存JSON文件的日志，显示文件名和目标目录
            logger.info(`${execLogPrefix}[阶段3/4] 开始保存JSON文件：${translatedSubtitleJsonName} 到 ${fileSaver.defaultBaseUploadsPath}`);


            // 如果 savePath 为空（null, undefined, 或空字符串），fileSaver.saveDataToFile 函数会使用其内部定义的默认基础上传路径（例如 fileSaver.defaultBaseUploadsPath）。
            // 将翻译后的JSON数组转换为格式化的字符串，并保存到指定文件
            // 使用正确的参数顺序调用 fileSaver.saveDataToFile
            const savedJsonPath = await fileSaver.saveDataToFile(
                JSON.stringify(translatedSubtitleJsonArray, null, 2), // 数据内容
                translatedSubtitleJsonName,                           // 文件名
                savePath,                     // 文件保存目录
                execLogPrefix                                         // 日志前缀
            );



            // 检查保存操作是否成功返回路径
            if (!savedJsonPath) {
                throw new Error(`保存翻译后的JSON文件 ${translatedSubtitleJsonName} 失败，fileSaver 未返回有效路径。`);
            }
            translatedSubtitleJsonPath = savedJsonPath; // 使用返回的完整路径
            // 记录JSON文件保存完成的日志，显示保存的条目数量和完整路径
            logger.info(`${execLogPrefix} JSON文件保存完成于：${translatedSubtitleJsonPath}，大小：${translatedSubtitleJsonArray.length}条`);




            // 阶段4: 生成SRT文件
            // 报告任务状态为运行中，子状态为生成SRT，并更新进度
            this.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.GENERATING_SRT, {
                detail: '生成中文字幕SRT',
                current: 85,
                total: 100
            });


            // 记录开始生成SRT内容的日志
            logger.debug(`${execLogPrefix}[阶段4/4] 开始生成SRT内容`);
            // 调用方法将翻译后的JSON数组转换为SRT格式的字符串
            chineseSrtContent = this.generateChineseSRT(translatedSubtitleJsonArray, execLogPrefix);
            // 构建中文字幕SRT文件的文件名（使用时间戳避免覆盖）
            const chineseSrtName = `${videoIdentifier}_translated_chinese_subtitle_${timestamp}.srt`;

            // 记录开始保存SRT文件的日志，显示文件名和目标目录
            logger.info(`${execLogPrefix} 开始保存SRT文件：${chineseSrtName} 到 ${fileSaver.defaultBaseUploadsPath}，内容长度：${chineseSrtContent.length}字符`);
            // 将SRT内容保存到指定文件
            // 使用正确的参数顺序调用 fileSaver.saveDataToFile
            const savedSrtPath = await fileSaver.saveDataToFile(
                chineseSrtContent,        // 数据内容
                chineseSrtName,           // 文件名
                savePath, // 基础目录
                execLogPrefix             // 日志前缀
            );

            // 检查保存操作是否成功返回路径
            if (!savedSrtPath) {
                throw new Error(`保存翻译后的SRT文件 ${chineseSrtName} 失败，fileSaver 未返回有效路径。`);
            }
            chineseSrtPath = savedSrtPath; // 使用返回的完整路径
            // 记录SRT文件保存完成的日志
            logger.info(`${execLogPrefix} SRT文件保存完成于：${chineseSrtPath}`);



            // 更新上下文并返回结果
            // 将翻译后JSON文件路径添加到上下文
            context.translatedSubtitleJsonPath = translatedSubtitleJsonPath;
            // 将中文字幕SRT文件路径添加到上下文
            context.chineseSrtPath = chineseSrtPath;
            // 将翻译后的字幕JSON数组添加到上下文
            context.translatedSubtitleJsonArray = translatedSubtitleJsonArray;
            // 将中文字幕SRT内容添加到上下文
            context.chineseSrtContent = chineseSrtContent;

            // 构建最终的任务结果对象
            const result = {
                translateSubtitleTaskStatus: 'success', // 定义任务状态为成功
                translatedSubtitleJsonArray: translatedSubtitleJsonArray, // 翻译后的字幕JSON数组
                translatedSubtitleJsonPath: translatedSubtitleJsonPath,  // 翻译后JSON文件路径
                chineseSrtContent: chineseSrtContent,           // 中文字幕SRT内容
                chineseSrtPath: chineseSrtPath, // 中文字幕SRT文件路径
            };

            // 调用父类方法，标记任务完成，并记录总耗时
            this.complete(result);
            // 记录任务执行成功的日志，显示总耗时
            logger.info(`${execLogPrefix} 任务执行成功，总耗时：${this.getElapsedTime()}ms`);

            // 返回任务结果
            return result;




        } catch (error) {
            // 捕获任务执行过程中发生的任何错误
            // 记录任务执行失败的日志，包含错误信息和堆栈
            logger.error(`${execLogPrefix} 任务执行失败，错误信息：${error.message}，堆栈：${error.stack}`);
            // 调用父类方法，标记任务失败，并传递错误对象
            this.fail(error);
            // 重新抛出错误，以便上层调用者能够捕获并处理
            throw error;
        }
    }




    async performLLMJsonTranslation(simplifiedSubtitleJsonArray, correctedFullText, videoIdentifier, execLogPrefix) {

        /*
         * 步骤1: 验证输入数据格式（新的5字段结构）
         * 确保输入的simplifiedSubtitleJsonArray符合标准5字段结构：id, start, end, text, words
         */
        if (!Array.isArray(simplifiedSubtitleJsonArray) || simplifiedSubtitleJsonArray.length === 0) {
            const errorMsg = '输入的简化字幕数据无效或为空';
            logger.error(`${execLogPrefix}[LLM翻译] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        // 验证5字段结构
        const firstItem = simplifiedSubtitleJsonArray[0];
        const requiredFields = ['id', 'start', 'end', 'text', 'words'];
        for (const field of requiredFields) {
            if (!(field in firstItem)) {
                const errorMsg = `输入数据缺少必需的5字段结构中的字段: ${field}`;
                logger.error(`${execLogPrefix}[LLM翻译] ${errorMsg}`);
                throw new Error(errorMsg);
            }
        }

        logger.info(`${execLogPrefix}[LLM翻译] 输入数据验证通过，包含${simplifiedSubtitleJsonArray.length}个5字段结构的字幕条目`);

        /*
         * 步骤1.5: 优化LLM输入 - 移除words字段以减少token消耗
         * 创建一个简化版本用于LLM处理，只保留翻译必需的4个字段：id, start, end, text
         */
        const simplifiedForLLM = simplifiedSubtitleJsonArray.map(item => ({
            id: item.id,
            start: item.start,
            end: item.end,
            text: item.text
            // 移除 words 字段以减少token消耗和提高LLM准确性
        }));

        logger.info(`${execLogPrefix}[LLM翻译] 已创建简化版本用于LLM处理，移除words字段，减少token消耗`);

        /*
         * 步骤2: 使用标准化LLM调用接口（遵循llm_api_standard.mdc规范）
         */
        
        // 使用LLM专用进度报告方法，报告准备阶段
        this.reportLLMProgress('preparing', '准备LLM字幕翻译请求', {
            current: 30,
            total: 100,
            segmentsCount: simplifiedSubtitleJsonArray.length
        });

        // 准备标准化的提示词参数
        const promptParams = {
            sourceLanguage: 'English',
            targetLanguage: 'Chinese (Simplified)',
            json_subtitle_array_string: JSON.stringify(simplifiedForLLM),
            fullTranscriptionContext: correctedFullText || '无完整上下文文本',
            systemPromptContent: '你是一位专业字幕翻译专家，严格遵守JSON格式要求，确保输出内容符合JSON格式。'
        };

        // 标准化LLM调用选项（遵循llm_api_standard.mdc）
        const llmOptions = {
            // === 基础参数 ===
            promptParams: promptParams,
            templateName: 'default',
            //modelName: 'google/gemini-2.5-flash-preview-05-20',
            //modelName: 'google/gemini-2.5-flash-lite-preview-06-17',
            modelName: 'google/gemini-2.5-flash',
            temperature: 0.3,
            max_tokens: 20000,
            forceJsonOutput: true,
            validateJsonOutput: true,
            maxJsonValidationRetries: 2,
            retryCount: 3,
            retryDelay: 1000,
            
            // === API增强配置 ===
            apiEnhancements: {
                // 结构化输出 - 确保4字段翻译结构
                structuredOutput: {
                    enabled: true,
                    schema: {
                        name: 'subtitle_translation_response',
                        strict: true,
                        schema: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: {
                                        type: 'string',
                                        description: 'Unique identifier for the subtitle segment (keep same as input)'
                                    },
                                    start: {
                                        type: 'number',
                                        description: 'Start time in seconds (keep same as input)'
                                    },
                                    end: {
                                        type: 'number',
                                        description: 'End time in seconds (keep same as input)'
                                    },
                                    text: {
                                        type: 'string',
                                        description: 'Translated subtitle text content in Chinese'
                                    }
                                },
                                required: ['id', 'start', 'end', 'text'],
                                additionalProperties: false
                            }
                        }
                    }
                },
                
                // 高级重试策略
                advancedRetry: {
                    exponentialBackoff: true,
                    jitter: true,
                    maxDelay: 30000
                },
                
                // 自定义请求头
                customHeaders: {
                    'X-Task-Type': 'SubtitleTranslation',
                    'X-Processing-Mode': 'LLM-Enhanced',
                    'X-Schema-Version': 'v1.0'
                },
                
                // Message Transforms
                transforms: ['middle-out']
            }
        };

        logger.info(`${execLogPrefix}[LLM翻译] 使用标准化LLM接口，启用增强API模式和结构化输出`);
        logger.debug(`${execLogPrefix}[LLM翻译] 模型: ${llmOptions.modelName}, 温度: ${llmOptions.temperature}`);

        /*
         * 步骤3: 智能重试机制
         */
        const MAX_RETRY_ATTEMPTS = 3;
        let attempt = 1;

                        while (attempt <= MAX_RETRY_ATTEMPTS) {
            try {
                logger.info(`${execLogPrefix}[LLM翻译] 第${attempt}次尝试，输入条目数: ${simplifiedForLLM.length} (已优化，移除words字段)`);

                // 使用LLM专用进度报告方法，报告发送阶段
                this.reportLLMProgress('sending', '发送LLM翻译请求', {
                    current: 40,
                    total: 100,
                    modelName: llmOptions.modelName,
                    attempt: attempt
                });

                // 使用LLM专用进度报告方法，报告等待阶段
                this.reportLLMProgress('waiting', '等待LLM处理翻译请求', {
                    current: 50,
                    total: 100,
                    estimatedTimeRemaining: 60
                });

                // === 标准化LLM调用 ===
                const response = await llmService.callLLM('TRANSLATE_SUBTITLE', llmOptions);

                // 使用LLM专用进度报告方法，报告接收阶段
                this.reportLLMProgress('receiving', '接收LLM翻译响应', {
                    current: 60,
                    total: 100,
                    responseStatus: response?.status,
                    processedTextLength: response?.processedText?.length || 0
                });

                // 检查LLM响应有效性
                if (response.status !== 'success' || !response.processedText) {
                    const errorMsg = `LLM翻译失败。状态: ${response?.status}, 消息: ${response?.message}`;
                    logger.error(`${execLogPrefix}[LLM翻译] 第${attempt}次尝试失败: ${errorMsg}`);

                    if (attempt < MAX_RETRY_ATTEMPTS) {
                        logger.info(`${execLogPrefix}[LLM翻译] 准备进行第${attempt + 1}次重试...`);
                        attempt++;
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        continue;
                    } else {
                        const llmError = new Error(`${errorMsg}（建议：检查提示词模板和JSON格式要求）`);
                        this.fail(llmError);
                        throw llmError;
                    }
                }

                logger.info(`${execLogPrefix}[LLM翻译] 第${attempt}次尝试LLM响应成功，开始智能比对和修复`);

                // 记录增强功能使用情况
                if (response?.enhancedFeatures) {
                    logger.info(`${execLogPrefix}[LLM翻译] 增强功能使用情况: ${JSON.stringify(response.enhancedFeatures)}`);
                }

                try {
                    // 使用LLM专用进度报告方法，报告解析阶段
                    this.reportLLMProgress('parsing', '解析LLM翻译响应', {
                        current: 70,
                        total: 100,
                        responseLength: response.processedText?.length || 0
                    });

                    // 智能比对和修复LLM返回的翻译结果
                    const validatedResult = this.compareAndFixLLMTranslation(
                        response.processedText,
                        simplifiedSubtitleJsonArray,
                        execLogPrefix
                    );

                    logger.info(`${execLogPrefix}[LLM翻译] 第${attempt}次尝试成功完成，返回 ${validatedResult.length} 个翻译字幕条目`);

                    return validatedResult;

                } catch (comparisonError) {
                    logger.warn(`${execLogPrefix}[LLM翻译] 第${attempt}次尝试比对失败: ${comparisonError.message}`);

                    if (attempt < MAX_RETRY_ATTEMPTS) {
                        logger.info(`${execLogPrefix}[LLM翻译] 准备进行第${attempt + 1}次重试...`);
                        attempt++;
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        continue;
                    } else {
                        throw comparisonError;
                    }
                }

            } catch (error) {
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    logger.warn(`${execLogPrefix}[LLM翻译] 第${attempt}次尝试出错: ${error.message}，准备重试`);
                    attempt++;
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    continue;
                } else {
                    logger.error(`${execLogPrefix}[LLM翻译] 所有${MAX_RETRY_ATTEMPTS}次尝试均失败: ${error.message}`);
                    logger.debug(`${execLogPrefix}[调试信息] 原始输入数据长度：${simplifiedSubtitleJsonArray.length}，上下文长度：${correctedFullText ? correctedFullText.length : 0}`);
                    const taskError = new Error(`字幕翻译流程失败: ${error.message}（错误代码：TRANSLATE_LLM_FAILURE）`);
                    this.fail(taskError);
                    throw taskError;
                }
            }
        }
    }
    /**
     * @功能概述: 智能比对和修复LLM返回的翻译JSON（引入SubtitleClozeTask的机制）
     * @param {string} llmResponseText - LLM返回的JSON字符串
     * @param {Array} originalSubtitleArray - 原始字幕数组，用于比对和修复
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Array} 修复后的完整翻译字幕JSON数组
     */
    compareAndFixLLMTranslation(llmResponseText, originalSubtitleArray, execLogPrefix) {
        try {
            // 使用jsonValidator解析LLM返回的JSON
            const parseResult = extractAndParseJson(llmResponseText, `${execLogPrefix}[LLM JSON解析]`);

            if (!parseResult.success || !Array.isArray(parseResult.data)) {
                const errorMsg = `LLM返回的内容不是有效的JSON数组。解析错误: ${parseResult.error}`;
                logger.error(`${execLogPrefix}[LLM JSON解析] ${errorMsg}`);
                logger.debug(`${execLogPrefix}[LLM JSON解析] LLM原始响应 (前1000字符): ${llmResponseText.substring(0, 1000)}`);
                throw new Error(errorMsg);
            }

            const llmJsonArray = parseResult.data;
            logger.debug(`${execLogPrefix}[LLM JSON解析] 成功解析LLM返回的JSON数组，包含 ${llmJsonArray.length} 个条目`);

            // 执行智能比对和修复
            const comparisonResult = this.performIntelligentTranslationComparison(
                llmJsonArray,
                originalSubtitleArray,
                execLogPrefix
            );

            if (comparisonResult.canFix) {
                logger.info(`${execLogPrefix}[智能修复] 检测到可修复的差异，执行自动修复`);
                return comparisonResult.fixedArray;
            } else {
                logger.warn(`${execLogPrefix}[智能修复] 检测到无法修复的差异，需要重新请求LLM`);
                throw new Error(`LLM返回的JSON与原始数据差异过大，无法修复: ${comparisonResult.reason}`);
            }

        } catch (error) {
            logger.error(`${execLogPrefix}[智能比对] 比对和修复过程中出错: ${error.message}`);
            throw error;
        }
    }
    /**
     * @功能概述: 执行智能比对，判断LLM翻译JSON是否可修复
     * @param {Array} llmJsonArray - LLM返回的JSON数组
     * @param {Array} originalArray - 原始字幕数组
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Object} 比对结果 {canFix: boolean, fixedArray: Array, reason: string}
     */
    performIntelligentTranslationComparison(llmJsonArray, originalArray, execLogPrefix) {
        try {
            logger.debug(`${execLogPrefix}[智能比对] 开始比对LLM JSON (${llmJsonArray.length}条) 与原始数据 (${originalArray.length}条)`);

            // 1. 基础数量检查
            if (llmJsonArray.length !== originalArray.length) {
                return {
                    canFix: false,
                    fixedArray: null,
                    reason: `数组长度不匹配: LLM=${llmJsonArray.length}, 原始=${originalArray.length}`
                };
            }

            // 2. 逐条比对和修复统计
            let fixableCount = 0;
            let unfixableCount = 0;
            const fixedArray = [];
            const issues = [];

            for (let i = 0; i < originalArray.length; i++) {
                const original = originalArray[i];
                const llmItem = llmJsonArray[i];

                const itemResult = this.compareAndFixSingleTranslationItem(original, llmItem, i + 1, execLogPrefix);

                if (itemResult.canFix) {
                    fixableCount++;
                    fixedArray.push(itemResult.fixedItem);
                } else {
                    unfixableCount++;
                    issues.push(`条目${i + 1}: ${itemResult.reason}`);
                }
            }

            // 3. 判断整体修复可行性
            const totalItems = originalArray.length;
            const fixableRate = fixableCount / totalItems;
            const FIXABLE_THRESHOLD = 0.8; // 80%以上可修复才认为整体可修复

            logger.debug(`${execLogPrefix}[智能比对] 修复统计: 可修复=${fixableCount}, 不可修复=${unfixableCount}, 修复率=${(fixableRate * 100).toFixed(1)}%`);

            if (fixableRate >= FIXABLE_THRESHOLD) {
                logger.info(`${execLogPrefix}[智能比对] 修复率${(fixableRate * 100).toFixed(1)}%，判定为可修复`);
                return {
                    canFix: true,
                    fixedArray: fixedArray,
                    reason: `修复成功，修复率${(fixableRate * 100).toFixed(1)}%`
                };
            } else {
                logger.warn(`${execLogPrefix}[智能比对] 修复率${(fixableRate * 100).toFixed(1)}%，低于阈值${FIXABLE_THRESHOLD * 100}%，判定为不可修复`);
                return {
                    canFix: false,
                    fixedArray: null,
                    reason: `修复率过低(${(fixableRate * 100).toFixed(1)}%)，主要问题: ${issues.slice(0, 3).join('; ')}`
                };
            }

        } catch (error) {
            logger.error(`${execLogPrefix}[智能比对] 比对过程出错: ${error.message}`);
            return {
                canFix: false,
                fixedArray: null,
                reason: `比对过程异常: ${error.message}`
            };
        }
    }
    /**
     * @功能概述: 比对和修复单个翻译字幕条目
     * @param {Object} original - 原始字幕条目（包含完整的5字段结构）
     * @param {Object} llmItem - LLM返回的字幕条目（4字段结构：id, start, end, text）
     * @param {number} index - 条目索引（用于日志）
     * @param {string} execLogPrefix - 执行日志前缀
     * @returns {Object} 修复结果 {canFix: boolean, fixedItem: Object, reason: string}
     */
    compareAndFixSingleTranslationItem(original, llmItem, index, execLogPrefix) {
        try {
            // 检查LLM条目是否为对象
            if (!llmItem || typeof llmItem !== 'object') {
                return {
                    canFix: false,
                    fixedItem: null,
                    reason: `不是有效对象: ${typeof llmItem}`
                };
            }

            // 检查必需的text字段
            if (!llmItem.text || typeof llmItem.text !== 'string') {
                return {
                    canFix: false,
                    fixedItem: null,
                    reason: `缺少text字段或text字段不是字符串类型`
                };
            }

            // 验证LLM返回的4字段结构完整性
            const requiredFields = ['id', 'start', 'end', 'text'];
            const missingFields = requiredFields.filter(field => !(field in llmItem));
            
            if (missingFields.length > 0) {
                logger.debug(`${execLogPrefix}[条目${index}] LLM返回缺少字段: ${missingFields.join(', ')}, 将使用原始数据补充`);
            }

            // 构建修复后的条目（最终5字段结构，自动补充words字段）
            const fixedItem = {
                id: llmItem.id || original.id,                    // 优先使用LLM返回的id，否则使用原始id
                start: llmItem.start || original.start,           // 优先使用LLM返回的start，否则使用原始start
                end: llmItem.end || original.end,                 // 优先使用LLM返回的end，否则使用原始end
                text: llmItem.text,                               // 使用LLM翻译的text（这是唯一需要翻译的字段）
                words: original.words                             // 使用原始的words数组，因为LLM不再处理words字段
            };

            // 验证修复后的条目结构
            if (typeof fixedItem.start !== 'number' || typeof fixedItem.end !== 'number') {
                return {
                    canFix: false,
                    fixedItem: null,
                    reason: `时间戳字段类型错误: start=${typeof fixedItem.start}, end=${typeof fixedItem.end}`
                };
            }

            if (!Array.isArray(fixedItem.words)) {
                return {
                    canFix: false,
                    fixedItem: null,
                    reason: `words字段不是数组类型: ${typeof fixedItem.words}`
                };
            }

            logger.debug(`${execLogPrefix}[条目${index}] 翻译修复成功: "${original.text}" → "${llmItem.text}" (words字段已从原始数据补充)`);

            return {
                canFix: true,
                fixedItem: fixedItem,
                reason: `翻译修复成功（5字段结构，words字段已补充）`
            };

        } catch (error) {
            return {
                canFix: false,
                fixedItem: null,
                reason: `修复过程异常: ${error.message}`
            };
        }
    }







    parseAndValidateTranslation(translatedText, originalJson, execLogPrefix) {

        /*
         * 解析并验证翻译后的JSON数据，确保与原始结构一致。
         * 流程包括：
         * 1. 尝试从LLM的响应中提取并解析JSON。
         * 2. 增强日志记录，帮助调试和验证解析结果。
         * 3. 校验JSON格式和数组结构。
         * 4. 返回结构完整的字幕数组。
         * 5. 错误处理：
         *    - 当JSON解析失败时抛出"翻译结果不是有效的JSON格式"。
         *    - 当翻译后数组长度与原始不一致时抛出"翻译后JSON长度不一致"。
         * 6. 日志记录：
         *    - 包含execLogPrefix前缀，帮助追踪和调试。
         *    - 使用增强日志记录，帮助验证解析结果。
         *
         */


        let translatedJsonArray;

        /*
         * 尝试从LLM的响应中提取并解析JSON
         * 使用jsonValidator.js中的extractAndParseJson方法进行JSON解析
         * 增强日志记录，帮助调试和验证解析结果
         */

        try {


            // 尝试从LLM的响应中提取并解析JSON
            const parsedData = extractAndParseJson(translatedText, execLogPrefix); // 使用jsonValidator
            // ==> 增强日志记录 <==
            // 记录 parsedData 变量的类型，用于调试
            logger.debug(`${execLogPrefix} [验证器调试] parsedData 类型: ${typeof parsedData}`);
            if (parsedData) {
                // 记录 parsedData.success 字段的值，指示解析是否成功
                logger.debug(`${execLogPrefix} [验证器调试] parsedData.success: ${parsedData.success}`);
                // 记录 parsedData.data 字段的类型
                logger.debug(`${execLogPrefix} [验证器调试] parsedData.data 类型: ${typeof parsedData.data}`);
                // 检查并记录 parsedData.data 是否为数组
                logger.debug(`${execLogPrefix} [验证器调试] parsedData.data 是否为数组: ${Array.isArray(parsedData.data)}`);
                // 如果 parsedData.data 存在且不是数组，则记录其前500字符的内容，便于非数组情况下的调试
                if (parsedData.data && !Array.isArray(parsedData.data)) {
                    logger.debug(`${execLogPrefix} [验证器调试] parsedData.data 内容 (非数组时取前500字符): ${JSON.stringify(parsedData.data).substring(0, 500)}`);
                }
            } else {
                // 如果 parsedData 为空（null 或 undefined），则记录此信息
                logger.debug(`${execLogPrefix} [验证器调试] parsedData 为 null 或 undefined。`);
            }



            // ==> 增强日志记录结束 <==

            if (!parsedData || !parsedData.success || !Array.isArray(parsedData.data)) {
                 throw new Error('LLM响应不是有效的JSON数组或提取失败。');
            }
            translatedJsonArray = parsedData.data;
            logger.debug(`${execLogPrefix} LLM响应成功解析为JSON数组，包含 ${translatedJsonArray.length} 个条目。`);

        } catch (e) {
            logger.error(`${execLogPrefix} 解析LLM翻译结果失败: ${e.message}. 原始文本 (前500字符): ${translatedText.substring(0,500)}`);
            throw e;
        }

        // 将翻译后的文本合并回原始字幕结构。
        // `map` 方法遍历 `translatedJsonArray` 中的每个翻译条目。
        // 对于每个翻译条目 `item` 及其对应的 `index`，它会创建一个新对象。
        // `...originalJson[index]` 会将原始字幕对象（在相同索引位置）的所有属性复制到新对象中。
        // `text: item.text` 则会用翻译后的文本覆盖掉原始对象的 `text` 字段，
        // 从而保留原始字幕的 `id`, `start`, `end` 等信息，只更新文本内容。
        return translatedJsonArray.map((item, index) => ({
            ...originalJson[index],
            text: item.text // 仅替换text字段
        }));
    }



    generateChineseSRT(translatedJson, execLogPrefix) {
        logger.info(`${execLogPrefix}[步骤 6.2] 开始从翻译后 JSON 生成中文字幕SRT。`);

        // 输入类型验证（与英文版保持相同错误处理逻辑）
        if (!Array.isArray(translatedJson)) {
            const errorMsg = '生成中文SRT失败：输入的不是有效的字幕 JSON 数组。';
            logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        // 处理空数组的特殊情况（保持与英文版相同处理方式）
        if (translatedJson.length === 0) {
            logger.warn(`${execLogPrefix}[WARN] 翻译后字幕 JSON 数组为空，将生成空的 SRT 内容。`);
            return '';
        }

        let srtContent = '';
        translatedJson.forEach((item) => {
            // 字段格式验证（与英文版相同验证标准）
            if (!item || typeof item.id !== 'string' || typeof item.start !== 'number' ||
                typeof item.end !== 'number' || typeof item.text !== 'string') {
                logger.warn(`${execLogPrefix}[WARN][generateChineseSRT] 发现无效条目: ${JSON.stringify(item).substring(0, 100)}... 已跳过`);
                return; // 跳过无效条目，保持处理流程继续
            }

            // SRT块构建（保持与英文版相同的格式规范）
            srtContent += `${item.id}\r\n`; // ID行
            srtContent += `${this.formatTime(item.start)} --> ${this.formatTime(item.end)}\r\n`; // 时间轴行
            srtContent += `${item.text.trim()}\r\n\r\n`; // 文本行 + 双CRLF分隔符

            /* 设计说明：
               1. 使用\r\n保持Windows换行符标准，确保跨平台兼容性
               2. trim()文本内容去除首尾空白，提升可读性
               3. 双CRLF分隔符是SRT标准格式要求 */
        });

        // 统一处理末尾空行（与英文版相同实现）
        if (srtContent.endsWith('\r\n\r\n')) {
            srtContent = srtContent.slice(0, -2); // 移除最后两个CRLF字符
            /* 设计说明：
               移除多余空行是为了：
               - 符合SRT文件标准格式
               - 避免某些播放器解析问题
               - 保持与TranscriptionCorrectionTask生成结果的一致性 */
        }

        // 最终内容类型校验（防御性编程）
        if (typeof srtContent !== 'string') {
            const errorMsg = '生成的中文SRT内容无效';
            logger.error(`${execLogPrefix}[ERROR] ${errorMsg}`);
            throw new Error(errorMsg);
        }

        // 记录生成结果元数据（与英文版相同日志级别和格式）
        logger.info(`${execLogPrefix}[步骤 6.3] 中文字幕SRT生成完成。长度: ${srtContent.length} 字符`);
        logger.debug(`${execLogPrefix}[步骤 6.4] SRT预览 (前200字符): ${srtContent.substring(0, 200)}...`);

        return srtContent;
    }




    /**
     * @功能概述: 将秒数转换为SRT标准时间格式（HH:MM:SS,ms）
     * @参数说明:
     *   - seconds {number} 输入秒数（支持浮点数，如123.456秒）
     * @返回值 {string} SRT兼容的时间字符串格式：HH:MM:SS,ms
     * @错误处理:
     *   - 当输入非数字/负数时返回默认值00:00:00,000
     *   - 记录警告日志帮助调试
     *
     * @处理流程:
     *   1. 输入有效性校验（数值类型/范围）
     *   2. 创建Date对象（基于毫秒级精度）
     *   3. 分解时间分量（时/分/秒/毫秒）
     *   4. 各分量标准化处理（补零对齐）
     *
     * @设计决策:
     *   - 与TranscriptionCorrectionTask.js保持完全一致的实现
     *   - 使用UTC方法避免时区影响（SRT时间轴应为绝对时间）
     *   - 毫秒部分强制3位补零，确保字幕工具兼容性
     *   - 默认值选择00:00:00,000而非抛出错误，保证字幕文件结构完整
     *
     * @已知问题:
     *   - 浮点运算精度问题（例：0.9999999秒会进位为1秒）
     *   - 最大支持时长受Date对象限制（约285,616年）
     */
    formatTime(seconds) {
        // 输入有效性校验（类型检查 + 数值范围）
        if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
            logger.warn(`[WARN] formatTime 输入无效秒数: ${seconds}`);
            return '00:00:00,000'; // 返回安全默认值，保证字幕文件结构完整
        }

        // 时间分解（使用UTC方法避免时区干扰）
        const date = new Date(seconds * 1000); // 转换为毫秒
        const hours = String(date.getUTCHours()).padStart(2, '0');   // 小时补零对齐
        const minutes = String(date.getUTCMinutes()).padStart(2, '0'); // 分钟补零
        const sec = String(date.getUTCSeconds()).padStart(2, '0');    // 秒数补零
        const ms = String(date.getUTCMilliseconds()).padStart(3, '0'); // 毫秒强制3位

        // 格式组装（符合SRT标准格式要求）
        return `${hours}:${minutes}:${sec},${ms}`;
    }



    /**
     * @功能概述: 验证必需字段
     * @param {object} context - 上下文对象
     * @param {Array<string>} requiredFields - 必需字段列表
     * @param {string} execLogPrefix - 执行日志前缀
     * @throws {Error} 当缺少必需字段时
     */
    validateRequiredFields(context, requiredFields, execLogPrefix) {
        for (const field of requiredFields) {
            if (context[field] === undefined || context[field] === null) { // 更严格的检查
                const errorMsg = `执行失败：上下文缺少或包含无效的必需字段 "${field}"`;
                logger.error(`${execLogPrefix}[ERROR][字段校验] ${errorMsg}`);
                // 在抛出错误之前，将任务状态设置为 FAILED 并调用 this.fail() 发送进度回调
                this.fail(new Error(errorMsg)); // Call this.fail to trigger progress callback
                throw new Error(errorMsg);
            }
             // 特别检查 simplifiedSubtitleJsonArray 是否为数组 (如果字段存在)
             if (field === 'simplifiedSubtitleJsonArray' && context[field] !== undefined && !Array.isArray(context[field])) {
                 const errorMsg = `执行失败：必需字段 "simplifiedSubtitleJsonArray" 不是一个数组。`;
                 logger.error(`${execLogPrefix}[ERROR][字段校验] ${errorMsg}`);
                 // 在抛出错误之前，将任务状态设置为 FAILED 并调用 this.fail() 发送进度回调
                 this.fail(new Error(errorMsg)); // Call this.fail to trigger progress callback
                 throw new Error(errorMsg);
             }
        }
        logger.debug(`${execLogPrefix}[字段校验] 所有必需字段 (${requiredFields.join(', ')}) 验证通过。`);
    }

    /**
     * @功能概述: 收集详细上下文信息（基于TaskBase标准）
     * @说明: 遵循TaskBase.js标准，提供字幕翻译任务的详细上下文信息
     * @返回: {object} 包含任务详细信息的上下文对象
     */
    collectDetailedContext() {
        const logPrefix = `${this.instanceLogPrefix}[collectDetailedContext]`;

        try {
            // 获取基础上下文信息（继承自TaskBase）
            const baseContext = super.collectDetailedContext();

            // 从任务结果中提取翻译信息
            const taskResult = this.result || {};

            // 扩展输入上下文信息（覆盖基类的基础结构）
            const inputContext = {
                ...baseContext.inputContext,
                simplifiedSubtitleJsonReceived: taskResult.simplifiedSubtitleJsonArray ? true : false,
                subtitleEntriesCount: taskResult.simplifiedSubtitleJsonArray?.length || 'N/A',
                correctedFullTextReceived: taskResult.correctedFullText ? true : false,
                correctedFullTextLength: taskResult.correctedFullText ? taskResult.correctedFullText.length : 0,
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                inputFormat: 'simplified_subtitle_json_array',
                inputSource: 'transcription_correction_system'
            };

            // 扩展输出上下文信息（覆盖基类的基础结构）
            const outputContext = {
                ...baseContext.outputContext,
                translatedSubtitleJsonArray: taskResult.translatedSubtitleJsonArray || 'N/A',
                translatedEntriesCount: taskResult.translatedSubtitleJsonArray ? taskResult.translatedSubtitleJsonArray.length : 0,
                translatedSubtitleJsonPath: taskResult.translatedSubtitleJsonPath || 'N/A',
                chineseSrtPath: taskResult.chineseSrtPath || 'N/A',
                chineseSrtContent: taskResult.chineseSrtContent ? 'generated' : 'N/A',
                chineseSrtLength: taskResult.chineseSrtContent ? taskResult.chineseSrtContent.length : 0,
                translationQuality: 'llm_enhanced',
                outputFormat: 'multiple_formats',
                processingSuccess: taskResult.translateSubtitleTaskStatus === 'success'
            };

            // 扩展技术细节信息（覆盖基类的基础结构）
            const technicalDetails = {
                ...baseContext.technicalDetails,
                taskType: 'TranslateSubtitle',
                llmProvider: 'Google Gemini',
                llmModel: 'google/gemini-2.5-flash-preview-05-20',
                supportedInputFormats: ['simplified_subtitle_json_array'],
                outputFormat: 'translated_subtitle',
                processingMode: 'llm_translation',
                timeout: 60000,
                timeoutManagement: 'request_timeout',
                jsonValidationEnabled: true,
                fileSavingEnabled: true,
                retryEnabled: true
            };

            // 字幕翻译特定信息
            const subtitleTranslationDetails = {
                processingSteps: [
                    '参数验证',
                    'LLM翻译处理',
                    '翻译结果验证',
                    '翻译JSON保存',
                    'SRT生成',
                    '文件保存'
                ],
                currentStep: this.status === TASK_STATUS.COMPLETED ? '文件保存' :
                           this.status === TASK_STATUS.FAILED ? '错误处理' : '执行中',
                stepProgress: this.status === TASK_STATUS.COMPLETED ? '6/6' :
                            this.status === TASK_STATUS.FAILED ? 'N/A' : 'N/A',
                processingMethod: 'llm_translation',
                qualityLevel: 'high',
                translationStrategy: 'context_aware_translation',
                sourceLanguage: 'English',
                targetLanguage: 'Chinese (Simplified)'
            };

            // LLM交互详情（任务特定）
            const llmDetails = {
                provider: 'Google Gemini',
                model: 'google/gemini-2.5-flash-preview-05-20',
                endpoint: 'openrouter_api',
                requestMethod: 'POST',
                responseFormat: 'json',
                errorHandling: 'comprehensive_with_retry',
                retryStrategy: 'enabled',
                promptTemplate: 'TRANSLATE_SUBTITLE.default',
                temperature: 0.3,
                maxTokens: 20000,
                forceJsonOutput: true,
                connectionStatus: this.status === TASK_STATUS.COMPLETED ? 'success' :
                               this.status === TASK_STATUS.FAILED ? 'failed' : 'unknown'
            };

            // 输入字幕信息（任务特定）
            const inputSubtitleInfo = {
                simplifiedSubtitleJsonReceived: taskResult.simplifiedSubtitleJsonArray ? true : false,
                subtitleEntriesCount: taskResult.simplifiedSubtitleJsonArray?.length || 'N/A',
                correctedFullTextReceived: taskResult.correctedFullText ? true : false,
                correctedFullTextLength: taskResult.correctedFullText ? taskResult.correctedFullText.length : 0,
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                inputFormat: 'simplified_subtitle_json_array',
                inputSource: 'transcription_correction_system',
                dataValidation: 'json_validator_enabled'
            };

            // 输出翻译信息（任务特定）
            const outputTranslationInfo = {
                translatedSubtitleJsonArray: taskResult.translatedSubtitleJsonArray || 'N/A',
                translatedEntriesCount: taskResult.translatedSubtitleJsonArray ? taskResult.translatedSubtitleJsonArray.length : 0,
                translatedSubtitleJsonPath: taskResult.translatedSubtitleJsonPath || 'N/A',
                chineseSrtPath: taskResult.chineseSrtPath || 'N/A',
                chineseSrtContent: taskResult.chineseSrtContent ? 'generated' : 'N/A',
                chineseSrtLength: taskResult.chineseSrtContent ? taskResult.chineseSrtContent.length : 0,
                translationQuality: 'llm_enhanced',
                outputFormat: 'multiple_formats',
                processingSuccess: taskResult.translateSubtitleTaskStatus === 'success',
                filesGenerated: [
                    taskResult.translatedSubtitleJsonPath ? 'translated_subtitle.json' : null,
                    taskResult.chineseSrtPath ? 'chinese_subtitle.srt' : null
                ].filter(Boolean)
            };

            // 翻译参数详情（任务特定）
            const translationParameters = {
                videoIdentifier: taskResult.videoIdentifier || 'N/A',
                savePath: taskResult.savePath || 'N/A',
                translationMethod: 'llm_with_context',
                sourceLanguage: 'English',
                targetLanguage: 'Chinese (Simplified)',
                promptStrategy: 'context_aware_translation',
                responseValidation: 'json_validator_enabled',
                retryEnabled: true,
                llmTimeout: 60000
            };

            // 翻译历史详情（任务特定）
            const translationHistory = {
                translationType: 'llm_based',
                inputFormat: 'simplified_subtitle_json_array',
                outputFormat: 'multiple_translated_formats',
                translationMethod: 'google_gemini',
                processingTime: this.getElapsedTime(),
                translationSuccess: this.status === TASK_STATUS.COMPLETED,
                errorOccurred: this.status === TASK_STATUS.FAILED,
                lastError: this.error ? {
                    message: this.error.message,
                    name: this.error.name,
                    type: this.error.constructor.name
                } : null,
                progressUpdatesCount: this.progressHistory.length,
                lastProgressUpdate: this.progressHistory.length > 0 ?
                    this.progressHistory[this.progressHistory.length - 1] : null
            };

            // 合并所有上下文信息（遵循TaskBase标准结构）
            const extendedContext = {
                // 基础信息（来自TaskBase）
                taskInfo: baseContext.taskInfo,
                executionStats: baseContext.executionStats,
                progressHistory: baseContext.progressHistory,

                // 扩展的上下文信息（覆盖基类默认值）
                inputContext,
                outputContext,
                technicalDetails,

                // 任务特定的详细信息
                subtitleTranslationDetails,
                llmDetails,
                inputSubtitleInfo,
                outputTranslationInfo,
                translationParameters,
                translationHistory,

                // 元信息
                collectedAt: new Date().toISOString(),
                collectionMethod: 'TranslateSubtitleTask.collectDetailedContext'
            };

            logger.debug(`${logPrefix} 成功收集详细上下文信息，包含 ${Object.keys(extendedContext).length} 个主要部分`);
            return extendedContext;

        } catch (error) {
            logger.error(`${logPrefix} 收集详细上下文信息时出错: ${error.message}`);

            // 返回基础上下文和错误信息
            const baseContext = super.collectDetailedContext();
            return {
                ...baseContext,
                subtitleTranslationError: {
                    message: error.message,
                    stack: error.stack
                },
                collectedAt: new Date().toISOString(),
                collectionMethod: 'TranslateSubtitleTask.collectDetailedContext (with error)'
            };
        }
    }
}

module.exports = TranslateSubtitleTask;
logger.info(`${taskModuleLogPrefix}TranslateSubtitleTask 类已导出。`);