/**
 * @功能概述: TranslateSubtitleTask 任务类的单元测试
 * @说明: 测试字幕翻译任务的各种执行场景，包括LLM翻译和文件保存
 * @架构位置: 测试层，验证TranslateSubtitleTask的功能正确性
 * @测试覆盖: 正常流程、参数验证、错误处理、LLM交互、文件保存等
 */

// 导入测试所需的核心模块
const fs = require('fs');
const path = require('path');
const TranslateSubtitleTask = require('../TranslateSubtitleTask');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const logger = require('../../utils/logger');

// 测试日志前缀，用于标识测试输出
const testLogPrefix = '[测试：TranslateSubtitleTask.test.js]';

// 记录测试文件加载
logger.info(`${testLogPrefix} 测试文件已加载。`);

/**
 * @功能概述: 断言函数，用于验证条件是否为真
 * @param {boolean} condition - 要验证的条件
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当条件为假时抛出错误
 */
function assert(condition, message) {
    if (!condition) {
        const fullMessage = `断言失败: ${message}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(fullMessage);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

/**
 * @功能概述: 相等断言函数，验证两个值是否相等
 * @param {any} actual - 实际值
 * @param {any} expected - 期望值
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当值不相等时抛出错误
 */
function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: "${expected}", 实际: "${actual}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: "${actual}")`);
}

/**
 * @功能概述: 包含断言函数，验证字符串或数组是否包含指定子串
 * @param {string|Array} arrayOrString - 要检查的字符串或数组
 * @param {string} substring - 要查找的子串
 * @param {string} message - 断言失败时的错误消息
 * @throws {Error} 当不包含指定子串时抛出错误
 */
function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

/**
 * @功能概述: 创建测试用的简化字幕JSON数组
 * @returns {Array} 模拟的简化字幕数据
 */
function createTestSimplifiedSubtitleJsonArray() {
    return [
        {
            id: '1',
            start: 0.0,
            end: 2.5,
            text: 'Hello world.',
            words: [
                { text: 'Hello', start: 0.0, end: 1.0 },
                { text: 'world.', start: 1.0, end: 2.5 }
            ]
        },
        {
            id: '2',
            start: 2.5,
            end: 5.0,
            text: 'This is a test transcription.',
            words: [
                { text: 'This', start: 2.5, end: 3.0 },
                { text: 'is', start: 3.0, end: 3.2 },
                { text: 'a', start: 3.2, end: 3.3 },
                { text: 'test', start: 3.3, end: 3.8 },
                { text: 'transcription.', start: 3.8, end: 5.0 }
            ]
        }
    ];
}

/**
 * @功能概述: 加载真实的字幕JSON数据
 * @returns {Array} 真实的字幕数据
 */
function loadRealSubtitleJsonData() {
    try {
        const jsonFilePath = path.join(__dirname, '../../../uploads/input/test2222_video_b1ogtn_real_llm_corrected.json');
        logger.info(`${testLogPrefix} 尝试加载真实字幕数据: ${jsonFilePath}`);
        
        if (!fs.existsSync(jsonFilePath)) {
            throw new Error(`JSON文件不存在: ${jsonFilePath}`);
        }
        
        const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
        const subtitleData = JSON.parse(jsonContent);
        
        logger.info(`${testLogPrefix} 成功加载真实字幕数据，包含 ${subtitleData.length} 个字幕条目`);
        return subtitleData;
    } catch (error) {
        logger.error(`${testLogPrefix} 加载真实字幕数据失败: ${error.message}`);
        throw error;
    }
}

/**
 * @功能概述: 清理测试文件
 * @param {string} filePath - 要删除的文件路径
 */
function cleanupTestFile(filePath) {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            logger.debug(`${testLogPrefix} 清理测试文件: ${filePath}`);
        }
    } catch (error) {
        logger.warn(`${testLogPrefix} 清理测试文件失败: ${error.message}`);
    }
}

/**
 * @功能概述: 主测试执行函数
 */
async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 TranslateSubtitleTask 测试 ==========`);
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new TranslateSubtitleTask();
        assert(task instanceof TranslateSubtitleTask, '任务应为 TranslateSubtitleTask 的实例');
        assertEquals(task.name, 'TranslateSubtitleTask', '任务名称应为 TranslateSubtitleTask');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
        assert(task.taskId.includes('TranslateSubtitleTask'), '任务ID应包含任务名称');
    });

    await runSingleTest('2. 缺少必需字段 - videoIdentifier', async () => {
        const task = new TranslateSubtitleTask();
        const context = { 
            simplifiedSubtitleJsonArray: createTestSimplifiedSubtitleJsonArray(),
            savePath: '/test/save'
        }; // 缺少 videoIdentifier
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少或包含无效的必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'videoIdentifier', '错误消息应指明缺少videoIdentifier字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('3. 缺少必需字段 - simplifiedSubtitleJsonArray', async () => {
        const task = new TranslateSubtitleTask();
        const context = { 
            videoIdentifier: 'test-video',
            savePath: '/test/save'
        }; // 缺少 simplifiedSubtitleJsonArray
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少或包含无效的必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'simplifiedSubtitleJsonArray', '错误消息应指明缺少simplifiedSubtitleJsonArray字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('4. 缺少必需字段 - savePath', async () => {
        const task = new TranslateSubtitleTask();
        const context = { 
            videoIdentifier: 'test-video',
            simplifiedSubtitleJsonArray: createTestSimplifiedSubtitleJsonArray()
        }; // 缺少 savePath
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少或包含无效的必需字段', '错误消息应指明缺少字段');
            assertIncludes(error.message, 'savePath', '错误消息应指明缺少savePath字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('5. 无效的simplifiedSubtitleJsonArray - 不是数组', async () => {
        const task = new TranslateSubtitleTask();
        const context = {
            videoIdentifier: 'test-video',
            simplifiedSubtitleJsonArray: 'not an array', // 不是数组
            savePath: __dirname
        };
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '不是一个数组', '错误消息应指明数组类型问题');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('6. correctedFullText 可选参数测试 - 有值', async () => {
        const task = new TranslateSubtitleTask();
        const testSubtitleArray = createTestSimplifiedSubtitleJsonArray();
        const testCorrectedFullText = 'This is the corrected full text for context.';
        
        // 模拟LLM翻译方法，避免实际调用LLM
        task.performLLMJsonTranslation = async (subtitleArray, correctedText, videoId, logPrefix) => {
            // 验证correctedFullText被正确传递
            assert(correctedText === testCorrectedFullText, 'correctedFullText应被正确传递');
            
            // 返回模拟的翻译结果
            return subtitleArray.map(item => ({
                ...item,
                text: `翻译：${item.text}`
            }));
        };
        
        // 模拟文件保存方法
        task.generateChineseSRT = () => '1\r\n00:00:00,000 --> 00:00:02,500\r\n翻译：Hello world.\r\n\r\n';
        
        const context = {
            videoIdentifier: 'test-video-with-context',
            simplifiedSubtitleJsonArray: testSubtitleArray,
            correctedFullText: testCorrectedFullText,
            savePath: __dirname
        };
        
        // 这个测试主要验证参数传递，不执行完整流程
        try {
            // 只测试LLM方法调用
            const result = await task.performLLMJsonTranslation(
                testSubtitleArray, 
                testCorrectedFullText, 
                'test-video-with-context', 
                '[测试]'
            );
            assert(result.length === testSubtitleArray.length, '翻译结果数量应与输入一致');
            assert(result[0].text.includes('翻译：'), '翻译结果应包含翻译标记');
        } catch (error) {
            // 如果是LLM相关错误，这是预期的（因为我们没有真实的LLM环境）
            if (error.message.includes('LLM') || error.message.includes('翻译')) {
                logger.info(`${testLogPrefix} LLM相关错误是预期的: ${error.message}`);
            } else {
                throw error;
            }
        }
    });

    await runSingleTest('7. correctedFullText 可选参数测试 - 无值', async () => {
        const task = new TranslateSubtitleTask();
        const testSubtitleArray = createTestSimplifiedSubtitleJsonArray();

        // 模拟LLM翻译方法
        task.performLLMJsonTranslation = async (subtitleArray, correctedText, videoId, logPrefix) => {
            // 验证correctedFullText为空时，方法内部会处理默认值
            // 这里我们验证传入的参数确实是undefined
            assert(correctedText === undefined, 'correctedFullText为空时应传入undefined');

            return subtitleArray.map(item => ({
                ...item,
                text: `翻译：${item.text}`
            }));
        };

        const context = {
            videoIdentifier: 'test-video-no-context',
            simplifiedSubtitleJsonArray: testSubtitleArray,
            // 不提供 correctedFullText
            savePath: __dirname
        };

        try {
            // 只测试LLM方法调用
            const result = await task.performLLMJsonTranslation(
                testSubtitleArray,
                undefined, // 明确传递undefined
                'test-video-no-context',
                '[测试]'
            );
            assert(result.length === testSubtitleArray.length, '翻译结果数量应与输入一致');
        } catch (error) {
            // 如果是LLM相关错误，这是预期的
            if (error.message.includes('LLM') || error.message.includes('翻译')) {
                logger.info(`${testLogPrefix} LLM相关错误是预期的: ${error.message}`);
            } else {
                throw error;
            }
        }
    });

    // === 新增：真实LLM翻译测试 ===
    await runSingleTest('8. 真实LLM翻译测试 - 使用真实字幕数据 + Token优化', async () => {
        logger.info(`${testLogPrefix} 开始真实LLM翻译测试...`);
        
        const task = new TranslateSubtitleTask();
        const progressLogs = [];
        
        // 加载真实的字幕数据
        const realSubtitleData = loadRealSubtitleJsonData();
        
        // 计算Token优化效果
        const originalTokenCount = JSON.stringify(realSubtitleData).length;
        const simplifiedForLLM = realSubtitleData.map(item => ({
            id: item.id,
            start: item.start,
            end: item.end,
            text: item.text
        }));
        const optimizedTokenCount = JSON.stringify(simplifiedForLLM).length;
        
        logger.info(`${testLogPrefix} ========== Token优化效果分析 ==========`);
        logger.info(`${testLogPrefix} 原始5字段数据: ${originalTokenCount} 字符`);
        logger.info(`${testLogPrefix} 优化4字段数据: ${optimizedTokenCount} 字符`);
        logger.info(`${testLogPrefix} Token节省: ${originalTokenCount - optimizedTokenCount} 字符 (${(((originalTokenCount - optimizedTokenCount) / originalTokenCount) * 100).toFixed(1)}%)`);
        logger.info(`${testLogPrefix} 数据条目数: ${realSubtitleData.length} 个字幕段`);
        
        // 硬编码保存路径
        const hardCodedSavePath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\output';
        
        // 构建测试上下文
        const context = {
            videoIdentifier: 'test2222_video_b1ogtn_real_test',
            simplifiedSubtitleJsonArray: realSubtitleData,
            correctedFullText: '这是一个关于新闻播报的视频，内容包括特朗普在洛杉矶的抗议活动、加拿大的野火紧急情况、各种国际新闻等。',
            savePath: hardCodedSavePath
        };
        
        logger.info(`${testLogPrefix} 准备执行真实LLM翻译，字幕条目数: ${realSubtitleData.length}`);
        logger.info(`${testLogPrefix} 保存路径: ${hardCodedSavePath}`);
        
        // 确保输出目录存在
        if (!fs.existsSync(hardCodedSavePath)) {
            fs.mkdirSync(hardCodedSavePath, { recursive: true });
            logger.info(`${testLogPrefix} 创建输出目录: ${hardCodedSavePath}`);
        }
        
        try {
            // 执行真实的翻译任务
            const result = await task.execute(context, (data) => {
                progressLogs.push(data);
                logger.info(`${testLogPrefix} [进度] ${data.status} - ${data.substatus} - ${data.detail}`);
            });
            
            // 验证翻译结果
            assert(result, '翻译任务应返回结果');
            assertEquals(result.translateSubtitleTaskStatus, 'success', '翻译任务状态应为成功');
            assert(result.translatedSubtitleJsonArray, '应包含翻译后的字幕数组');
            assert(result.translatedSubtitleJsonPath, '应包含翻译后JSON文件路径');
            assert(result.chineseSrtPath, '应包含中文SRT文件路径');
            assert(result.chineseSrtContent, '应包含中文SRT内容');
            
            // 验证翻译结果数量
            assertEquals(result.translatedSubtitleJsonArray.length, realSubtitleData.length, 
                        '翻译后的字幕数量应与原始数量一致');
            
            // 验证文件是否确实被保存
            assert(fs.existsSync(result.translatedSubtitleJsonPath), '翻译后的JSON文件应存在');
            assert(fs.existsSync(result.chineseSrtPath), '中文SRT文件应存在');
            
            // 记录LLM响应详情
            logger.info(`${testLogPrefix} ========== LLM翻译结果详情 ==========`);
            logger.info(`${testLogPrefix} 原始字幕条目数: ${realSubtitleData.length}`);
            logger.info(`${testLogPrefix} 翻译后字幕条目数: ${result.translatedSubtitleJsonArray.length}`);
            logger.info(`${testLogPrefix} 翻译后JSON文件: ${result.translatedSubtitleJsonPath}`);
            logger.info(`${testLogPrefix} 中文SRT文件: ${result.chineseSrtPath}`);
            logger.info(`${testLogPrefix} SRT内容长度: ${result.chineseSrtContent.length} 字符`);
            
            // 显示前几个翻译示例
            logger.info(`${testLogPrefix} ========== 翻译示例 ==========`);
            for (let i = 0; i < Math.min(5, result.translatedSubtitleJsonArray.length); i++) {
                const original = realSubtitleData[i];
                const translated = result.translatedSubtitleJsonArray[i];
                logger.info(`${testLogPrefix} [示例${i+1}]`);
                logger.info(`${testLogPrefix}   原文: ${original.text}`);
                logger.info(`${testLogPrefix}   译文: ${translated.text}`);
                logger.info(`${testLogPrefix}   时间: ${original.start}s - ${original.end}s`);
            }
            
            // 记录任务执行统计
            logger.info(`${testLogPrefix} ========== 任务执行统计 ==========`);
            logger.info(`${testLogPrefix} 任务状态: ${task.status}`);
            logger.info(`${testLogPrefix} 执行时长: ${task.getElapsedTime()}ms`);
            logger.info(`${testLogPrefix} 进度回调次数: ${progressLogs.length}`);
            
            logger.info(`${testLogPrefix} ✅ 真实LLM翻译测试成功完成！`);
            
        } catch (error) {
            logger.error(`${testLogPrefix} ❌ 真实LLM翻译测试失败: ${error.message}`);
            logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
            throw error;
        }
    });

    await runSingleTest('9. 进度回调功能', async () => {
        const task = new TranslateSubtitleTask();
        const progressLogs = [];

        // 设置进度回调
        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试进度报告
        task.reportProgress(TASK_STATUS.RUNNING, TASK_SUBSTATUS.PROCESSING, {
            detail: '测试进度报告',
            current: 50,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录进度回调');
        assertEquals(progressLogs[0].taskName, 'TranslateSubtitleTask', '进度回调应包含正确的任务名称');
        assertEquals(progressLogs[0].status, TASK_STATUS.RUNNING, '进度回调应包含正确的状态');
    });

    await runSingleTest('10. LLM进度报告功能', async () => {
        const task = new TranslateSubtitleTask();
        const progressLogs = [];

        task.setProgressCallback((data) => progressLogs.push(data));

        // 测试LLM专用进度报告
        task.reportLLMProgress('translating', '正在翻译字幕', {
            current: 30,
            total: 100
        });

        assert(progressLogs.length > 0, '应记录LLM进度回调');
        assertEquals(progressLogs[0].taskName, 'TranslateSubtitleTask', 'LLM进度回调应包含正确的任务名称');
        assert(progressLogs[0].technicalDetail, 'LLM进度回调应包含技术详情');
    });

    await runSingleTest('11. 任务状态管理', async () => {
        const task = new TranslateSubtitleTask();

        // 测试初始状态
        assertEquals(task.status, TASK_STATUS.PENDING, '初始状态应为PENDING');

        // 测试开始状态
        task.start();
        assertEquals(task.status, TASK_STATUS.STARTED, '开始后状态应为STARTED');
        assert(task.startTime, '应记录开始时间');

        // 测试完成状态
        const testResult = { translateSubtitleTaskStatus: 'success' };
        task.complete(testResult);
        assertEquals(task.status, TASK_STATUS.COMPLETED, '完成后状态应为COMPLETED');
        assertEquals(task.result, testResult, '应保存任务结果');
        assert(task.endTime, '应记录结束时间');

        // 测试执行时长
        const duration = task.getElapsedTime();
        assert(duration >= 0, '执行时长应为非负数');
    });

    await runSingleTest('12. 错误处理和失败状态', async () => {
        const task = new TranslateSubtitleTask();
        const testError = new Error('测试错误');

        // 测试失败状态
        task.fail(testError);
        assertEquals(task.status, TASK_STATUS.FAILED, '失败后状态应为FAILED');
        assertEquals(task.error, testError, '应保存错误对象');
        assert(task.endTime, '失败时应记录结束时间');
    });

    await runSingleTest('13. collectDetailedContext 方法', async () => {
        const task = new TranslateSubtitleTask();
        const context = task.collectDetailedContext();

        assert(context, 'collectDetailedContext应返回上下文对象');
        assert(context.taskInfo, '上下文应包含taskInfo');
        assert(context.executionStats, '上下文应包含executionStats');
        assert(context.progressHistory, '上下文应包含progressHistory');
        assert(context.inputContext, '上下文应包含inputContext');
        assert(context.outputContext, '上下文应包含outputContext');
        assert(context.technicalDetails, '上下文应包含technicalDetails');
        assert(context.subtitleTranslationDetails, '上下文应包含subtitleTranslationDetails');
        assert(context.llmDetails, '上下文应包含llmDetails');
        assertEquals(context.collectionMethod, 'TranslateSubtitleTask.collectDetailedContext',
                    '收集方法应正确标识');
    });

    await runSingleTest('14. parseAndValidateTranslation 方法', async () => {
        const task = new TranslateSubtitleTask();
        const execLogPrefix = '[测试]';

        const originalJson = createTestSimplifiedSubtitleJsonArray();
        const translatedText = JSON.stringify([
            { text: '你好世界。' },
            { text: '这是一个测试转录。' }
        ]);

        try {
            const result = task.parseAndValidateTranslation(translatedText, originalJson, execLogPrefix);

            assert(Array.isArray(result), '结果应为数组');
            assertEquals(result.length, originalJson.length, '结果数组长度应与原始数组一致');
            assert(result[0].id, '结果项应保留原始id');
            assert(result[0].start !== undefined, '结果项应保留原始start时间');
            assert(result[0].end !== undefined, '结果项应保留原始end时间');
            assertEquals(result[0].text, '你好世界。', '结果项应包含翻译后的文本');
        } catch (error) {
            // 如果是JSON解析相关错误，记录但不失败测试
            if (error.message.includes('JSON') || error.message.includes('解析')) {
                logger.info(`${testLogPrefix} JSON解析相关错误是预期的: ${error.message}`);
            } else {
                throw error;
            }
        }
    });

    await runSingleTest('15. formatTime 方法', async () => {
        const task = new TranslateSubtitleTask();

        // 测试正常时间格式化
        const result1 = task.formatTime(65.5); // 1分5.5秒
        assertEquals(result1, '00:01:05,500', '应正确格式化时间');

        // 测试0秒
        const result2 = task.formatTime(0);
        assertEquals(result2, '00:00:00,000', '应正确格式化0秒');

        // 测试无效输入
        const result3 = task.formatTime(-1);
        assertEquals(result3, '00:00:00,000', '应对无效输入返回默认值');
    });

    await runSingleTest('16. generateChineseSRT 方法', async () => {
        const task = new TranslateSubtitleTask();
        const execLogPrefix = '[测试]';

        const testTranslatedJson = [
            { id: '1', start: 0.0, end: 2.0, text: '你好世界。' },
            { id: '2', start: 2.0, end: 4.0, text: '这是一个测试。' }
        ];

        const srtContent = task.generateChineseSRT(testTranslatedJson, execLogPrefix);

        assert(typeof srtContent === 'string', 'SRT内容应为字符串');
        assert(srtContent.length > 0, 'SRT内容不应为空');
        assertIncludes(srtContent, '1', 'SRT应包含序号');
        assertIncludes(srtContent, '你好世界。', 'SRT应包含中文文本内容');
        assertIncludes(srtContent, '-->', 'SRT应包含时间分隔符');
    });

    await runSingleTest('17. validateRequiredFields 方法', async () => {
        const task = new TranslateSubtitleTask();
        const execLogPrefix = '[测试]';

        // 测试有效字段
        const validContext = {
            videoIdentifier: 'test-video',
            simplifiedSubtitleJsonArray: createTestSimplifiedSubtitleJsonArray(),
            savePath: '/test/path'
        };

        try {
            task.validateRequiredFields(validContext, ['videoIdentifier', 'simplifiedSubtitleJsonArray', 'savePath'], execLogPrefix);
            assert(true, 'validateRequiredFields应接受有效上下文');
        } catch (error) {
            throw new Error(`validateRequiredFields不应对有效上下文抛出错误: ${error.message}`);
        }

        // 测试无效字段
        const invalidContext = {
            videoIdentifier: 'test-video',
            // 缺少 simplifiedSubtitleJsonArray
            savePath: '/test/path'
        };

        try {
            task.validateRequiredFields(invalidContext, ['videoIdentifier', 'simplifiedSubtitleJsonArray', 'savePath'], execLogPrefix);
            throw new Error('validateRequiredFields应对无效上下文抛出错误');
        } catch (error) {
            assertIncludes(error.message, '缺少或包含无效的必需字段', '错误消息应指明字段问题');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应设置为FAILED');
        }
    });

    // --- 测试总结 ---
    logger.info(`${testLogPrefix} ========== TranslateSubtitleTask 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 总计测试用例: ${testsPassed + testsFailed}`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed}`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed}`);

    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} ❌ 测试未全部通过。`);
        process.exit(1); // 以错误码退出，方便CI/CD集成
    } else {
        logger.info(`${testLogPrefix} ✅ 所有测试用例通过!`);
        process.exit(0); // 成功退出
    }
}

// 立即执行测试
runTests().catch(error => {
    logger.error(`${testLogPrefix} 测试脚本顶层捕获到未处理异常: ${error.message}`);
    process.exit(1);
});
