# IDE and Editor files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
Thumbs.db

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Node modules folders
## Root and workspace node_modules
node_modules/
/backend/node_modules/
/frontend/node_modules/

# Environment variable files
## Root .env (if any)
.env
## Backend .env
/backend/.env

# Build output directories
## Frontend build output (built on server)
/frontend/dist/

# Upload directories
## Backend uploads
/backend/uploads/

# Remotion generated files and dynamic assets
## Remotion public directory - contains dynamically copied static assets (audio, video, backgrounds)
/backend/src/remotion/public/
## Remotion generated directory - contains dynamically generated TSX component files
/backend/src/remotion/generated/

# Generated Artifacts / Reports
express-current-architecture.json
express-current-directory-structure.mmd
FileScopeMCP-tree.json

# Sensitive Files
doc/服务器信息_0520.md

# Added by Task Master AI
# Logs
logs
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
# OS specific
# Task files (修正规则，只忽略根目录的tasks配置文件)
tasks.json
/tasks/
# 确保src/tasks/源代码目录被跟踪
!src/tasks/
# Augment directory
.augment/
