/**
 * @功能概述: 循环重置的线性进度条组件，支持多遍重复播放的动态进度指示
 * @组件类型: Remotion进度组件（数据驱动）
 * @使用场景: 视频底部的进度指示器，每个播放周期从0%到100%循环
 * @核心特性: 支持循环重置动画，适配重复播放的音频/视频内容
 */

import React from 'react';
import { useCurrentFrame, useVideoConfig, interpolate } from 'remotion';

/**
 * @功能概述: 循环重置的线性进度条组件
 * @param {Object} props - 组件属性
 * @param {Object} props.config - 进度条配置对象（来自template）
 * @param {number} props.singleCycleDuration - 单次播放周期的帧数（用于循环计算）
 * @returns {JSX.Element} 进度条组件
 */
const LinearProgress = ({ config = {}, singleCycleDuration }) => {
    const frame = useCurrentFrame();
    const { durationInFrames, width, height } = useVideoConfig();

    // 获取单次播放周期帧数，优先使用传入的参数，回退到配置或总帧数
    const cycleDuration = singleCycleDuration || 
                         config.animation?.singleCycleDuration || 
                         durationInFrames;

    // 计算当前周期内的帧位置（核心循环逻辑）
    const currentCycleFrame = frame % cycleDuration;

    // 计算当前周期的进度百分比（每个周期都是0%到100%）
    const progress = interpolate(
        currentCycleFrame,
        [0, cycleDuration],
        [0, 1],
        {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp'
        }
    );

    // 获取位置和样式配置（完全来自props）
    const position = config.position || {};
    const style = config.style || {};

    // 计算实际像素值
    const progressBarHeight = typeof position.height === 'string' && position.height.includes('%') ?
        Math.round(height * (parseFloat(position.height) / 100)) :
        parseInt(position.height) || 50;

    const progressBarY = typeof position.top === 'string' && position.top.includes('%') ?
        Math.round(height * (parseFloat(position.top) / 100)) :
        parseInt(position.top) || 0;

    const progressBarWidth = typeof position.width === 'string' && position.width.includes('%') ?
        width * (parseFloat(position.width) / 100) :
        parseInt(position.width) || width;

    return (
        <div style={{
            position: 'absolute',
            left: position.left || '0%',
            top: progressBarY + 'px',
            width: progressBarWidth + 'px',
            height: progressBarHeight + 'px',
            backgroundColor: style.backgroundColor,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            zIndex: 5
        }}>
            {/* 进度填充条 */}
            <div style={{
                width: progress * progressBarWidth + 'px',
                height: '100%',
                backgroundColor: style.progressColor,
                borderRadius: style.borderRadius || '0px',
                transition: 'width 0.1s ease'
            }} />
        </div>
    );
};

export default LinearProgress;
