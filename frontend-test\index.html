<!DOCTYPE html>
<!-- 
  @功能概述: Express项目的前端测试页面主HTML文件。
  该页面使用Vue 3和Element Plus（通过CDN引入）构建，用于测试视频上传和处理相关的后端API。
  新增视频编辑状态，包含响应式视频播放器和动态字幕显示。
-->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Express项目前端测试</title>
    
    <!-- Element Plus CSS: 引入Element Plus UI组件库的样式文件 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <!-- Vue 3 CDN: 引入Vue 3核心库 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- VueUse CDN: 引入VueUse工具库 -->
    <script src="https://unpkg.com/@vueuse/core/index.iife.min.js"></script>

    <!-- Element Plus JS: 引入Element Plus UI组件库的JavaScript文件 -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>

    <!-- Element Plus 图标: 引入Element Plus图标库 -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <!-- Cropper.js CSS: 更改为 v1.5.13 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css" rel="stylesheet">

    <!-- 页面自定义样式 -->
    <link rel="stylesheet" href="./css/main.css">
</head>




<body>
    <!-- 
      @功能概述: Vue应用的根DOM元素。
      所有Vue组件和内容都将渲染到此div内部。
    -->
    <div id="app">
        <!-- 
          @组件: el-container (Element Plus)
          @功能概述: 整体页面布局容器，包含头部和主要内容区域。
        -->
        <el-container>
            <!-- 
              @组件: el-header (Element Plus)
              @功能概述: 页面头部区域，显示应用标题。
            -->
            <el-header>
                <h1>视频处理与编辑测试</h1>
            </el-header>
            <!-- 
              @组件: el-main (Element Plus)
              @功能概述: 页面主要内容区域，包含导航和各个功能区。
            -->
            <el-main>
                <!-- 
                  @功能概述: 导航按钮容器，用于在不同功能区之间切换。
                -->
                <div class="navigation-buttons">
                    <!-- 
                      @组件: el-button (Element Plus)
                      @功能: 切换到"上传视频"功能区。
                      @交互: 点击时将 activeSection 设置为 'upload'。
                      @样式: 当 activeSection 为 'upload' 时，按钮类型为 'primary'。
                      @状态: 当 activeSection 为 'processing' 或已有视频在编辑时禁用，且在视频编辑状态下隐藏。
                    -->
                    <el-button v-if="activeSection !== 'video-editor'" :type="activeSection === 'upload' ? 'primary' : 'default'" @click="navigateTo('upload')" :disabled="isUploading ">上传视频</el-button>

                    <!--
                      @组件: el-button (Element Plus)
                      @功能: 切换到"选择已有视频"功能区。
                      @交互: 点击时将 activeSection 设置为 'select-existing'，并自动加载项目列表。
                      @样式: 当 activeSection 为 'select-existing' 时，按钮类型为 'primary'。
                      @状态: 当 activeSection 为 'processing' 或已有视频在编辑时禁用，且在视频编辑状态下隐藏。
                    -->
                    <el-button v-if="activeSection !== 'video-editor'" :type="activeSection === 'select-existing' ? 'primary' : 'default'" @click="navigateTo('select-existing'); loadProjectList()" :disabled="isUploading">选择已有视频</el-button>

                    <!--
                      @组件: el-button (Element Plus)
                      @功能: "处理中"或"视频编辑中"状态指示按钮。
                      @条件渲染: 当正在上传 或 (处于视频编辑状态且有编辑数据) 时显示。
                      @样式: 使用 'info' 类型并显示加载图标。
                      @状态: 始终禁用，仅作为视觉指示。
                    -->
                    <div v-if="isUploading || (activeSection === 'video-editor' && editorData)" class="editing-status-container">
                        <el-button type="info" :icon="ElementPlusIconsVue.Loading" disabled>{{ editingIndicatorText }}</el-button>

                        <!--
                          @组件: el-button (Element Plus) - 退出编辑按钮
                          @功能: 退出编辑模式，返回上传界面
                          @条件渲染: 只在视频编辑状态下显示
                          @样式: 根据状态动态调整类型和禁用状态
                          @交互: 点击时调用 handleExitEditing 方法
                          @状态: 退出过程中显示加载状态，裁剪模式下可能禁用
                        -->
                        <el-button
                            v-if="activeSection === 'video-editor' && editorData"
                            :type="exitButtonState.type"
                            @click="handleExitEditing"
                            :loading="isExitingEdit"
                            :disabled="exitButtonState.disabled"
                            :icon="ElementPlusIconsVue.Close"
                            style="margin-left: 10px;"
                            :title="exitButtonState.tooltip">
                            {{ exitButtonState.buttonText }}
                        </el-button>
                    </div>
                </div>

                

                <!-- 
                  @功能概述: "上传视频"功能区。
                  @条件渲染: 当 activeSection 的值为 'upload' 时显示。
                -->
                <div v-if="activeSection === 'upload'" class="content-section">
                    <!-- 区域标题 -->
                    <h3>上传新视频</h3>
                    <!-- 
                      @元素: input (type="file")
                      @功能: 标准HTML文件选择输入框，用于选择本地视频文件。
                      @属性:
                        - ref="fileInput": Vue ref引用，用于在JavaScript中访问此DOM元素。
                        - @change="handleFileChange": 文件选择变化时触发的Vue事件处理器。
                        - accept="video/*": 限制可选文件类型为视频。
                        - style="display: none;": 隐藏此原生输入框，通过自定义按钮触发。
                    -->
                    <input type="file" ref="fileInput" accept="video/*" style="display: none;">
                    <!-- 
                      @组件: el-button (Element Plus)
                      @功能: 自定义的文件选择触发按钮。
                      @交互: 点击时调用 triggerFileInput 方法，间接触发隐藏的文件输入框。
                      @状态: 当正在上传或已有视频在编辑时禁用。
                    -->
                    <el-button type="primary" @click="triggerFileInput" :disabled="isUploading">选择视频文件</el-button>
                    <!-- 
                      @元素: p
                      @功能: 显示用户已选择的文件名。
                      @条件渲染: 当 selectedFile 存在时显示。
                    -->
                    <p v-if="selectedFile">已选择文件: {{ selectedFile.name }}</p>



                    <!-- 
                      @组件: el-button (Element Plus)
                      @功能: 触发视频上传操作的按钮。
                      @交互: 点击时调用 uploadVideo 方法。
                      @状态: 
                        - 当没有选择文件 (selectedFile 为 null) 或正在上传 (isUploading 为 true) 或已有视频在编辑时禁用。
                        - 按钮文本根据 isUploading 状态在 '开始上传' 和 '上传中...' 之间切换。
                    -->
                    <el-button type="success" @click="uploadVideo" :disabled="!selectedFile || isUploading">
                        {{ isUploading ? '上传中...' : '开始上传' }}
                    </el-button>
                    
                    <!--
                      @功能概述: 用于显示视频上传进度和服务器响应。
                    -->
                    <!-- <h4>上传状态信息:</h4>
                    <div id="uploadStatus">
                        <div v-for="(message, index) in uploadStatusMessages" :key="index">{{ message }}</div>
                        <span v-if="uploadStatusMessages.length === 0">等待上传...</span>
                    </div> -->
                </div>

                <!--
                  @功能概述: "选择已有视频"功能区。
                  @条件渲染: 当 activeSection 的值为 'select-existing' 时显示。
                  @内容: 包含项目列表、分页控制、项目选择和确认功能。
                -->
                <div v-if="activeSection === 'select-existing'" class="content-section">
                    <!-- 区域标题 -->
                    <h3>选择已有视频</h3>

                    <!-- 加载状态 -->
                    <div v-if="isLoadingProjects" class="loading-container">
                        <el-icon class="is-loading"><component :is="ElementPlusIconsVue.Loading"></component></el-icon>
                        <span>正在加载项目列表...</span>
                    </div>

                    <!-- 错误状态 -->
                    <div v-if="projectLoadError && !isLoadingProjects" class="error-container">
                        <el-alert
                            title="加载失败"
                            :description="projectLoadError"
                            type="error"
                            show-icon
                            :closable="false">
                        </el-alert>
                        <el-button type="primary" @click="loadProjectList()" style="margin-top: 10px;">
                            重新加载
                        </el-button>
                    </div>

                    <!-- 空状态 -->
                    <div v-if="!isLoadingProjects && !projectLoadError && projectList.length === 0" class="empty-container">
                        <el-empty description="暂无已上传的视频项目">
                            <el-button type="primary" @click="navigateTo('upload')">上传新视频</el-button>
                        </el-empty>
                    </div>

                    <!-- 项目列表 -->
                    <div v-if="!isLoadingProjects && !projectLoadError && projectList.length > 0" class="project-list-container">
                        <!-- 项目网格 -->
                        <div class="project-grid">
                            <div
                                v-for="project in projectList"
                                :key="project.videoIdentifier"
                                class="project-card"
                                :class="{ 'selected': selectedProject && selectedProject.videoIdentifier === project.videoIdentifier }"
                            >
                                <el-card shadow="hover" class="project-card-inner">
                                    <!-- 项目状态标识 -->
                                    <div class="project-status">
                                        <el-tag
                                            :type="project.projectStatus === 'completed' ? 'success' :
                                                   project.projectStatus === 'processed' ? 'warning' :
                                                   project.projectStatus === 'uploaded' ? 'info' : 'danger'"
                                            size="small">
                                            {{ project.projectStatus === 'completed' ? '已完成' :
                                               project.projectStatus === 'processed' ? '已处理' :
                                               project.projectStatus === 'uploaded' ? '仅上传' : '未知' }}
                                        </el-tag>
                                    </div>

                                    <!-- 项目信息 -->
                                    <div class="project-info">
                                        <h4 class="project-title" :title="project.originalVideoName">
                                            {{ project.originalVideoName }}
                                        </h4>
                                        <div class="project-meta-grid">
                                            <div class="project-meta-item">
                                                <i class="el-icon-document"></i>
                                                <span>{{ project.fileSize }}</span>
                                            </div>
                                            <div class="project-meta-item">
                                                <i class="el-icon-time"></i>
                                                <span>{{ new Date(project.uploadTime).toLocaleDateString() }}</span>
                                            </div>
                                            <div class="project-meta-item">
                                                <i class="el-icon-data-analysis"></i>
                                                <span>{{ project.hasProcessedData ? '已处理' : '未处理' }}</span>
                                            </div>
                                            <div class="project-meta-item">
                                                <i class="el-icon-folder"></i>
                                                <span>{{ project.projectStatus === 'completed' ? '已完成' :
                                                       project.projectStatus === 'processed' ? '已处理' :
                                                       project.projectStatus === 'uploaded' ? '仅上传' : '未知' }}</span>
                                            </div>
                                            <div class="project-meta-item">
                                                <i class="el-icon-video-camera"></i>
                                                <span>{{ project.generatedVideoCount || 0 }}个短视频</span>
                                            </div>
                                            <div class="project-meta-item">
                                                <i class="el-icon-upload"></i>
                                                <span>已发布 {{ project.publishedVideoCount || 0 }}/{{ project.generatedVideoCount || 0 }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="project-actions">
                                        <!-- 主操作按钮 -->
                                        <div class="primary-action">
                                            <el-button
                                                v-if="!selectedProject || selectedProject.videoIdentifier !== project.videoIdentifier"
                                                type="primary"
                                                size="default"
                                                :disabled="!project.hasOriginalVideo"
                                                @click="selectProject(project)"
                                                :title="project.hasOriginalVideo ? '选择此项目进行编辑' : '此项目缺少视频文件，无法编辑'"
                                                class="primary-select-btn">
                                                <i class="el-icon-check"></i>
                                                {{ project.hasOriginalVideo ? '选择项目' : '无视频文件' }}
                                            </el-button>
                                            <el-button
                                                v-if="selectedProject && selectedProject.videoIdentifier === project.videoIdentifier"
                                                type="success"
                                                size="default"
                                                @click="clearProjectSelection()"
                                                class="primary-select-btn">
                                                <i class="el-icon-circle-check"></i>
                                                已选择
                                            </el-button>
                                        </div>

                                        <!-- 次要操作按钮组 -->
                                        <div class="secondary-actions">
                                            <el-button
                                                type="warning"
                                                size="small"
                                                @click="renameProject(project)"
                                                :title="'重命名项目'"
                                                class="secondary-button">
                                                <i class="el-icon-edit"></i>
                                                重命名
                                            </el-button>
                                            <el-button
                                                type="info"
                                                size="small"
                                                @click="openGeneratedVideosDialog(project.videoIdentifier)"
                                                :title="'查看生成的短视频'"
                                                class="secondary-button">
                                                <i class="el-icon-video-camera"></i>
                                                查看视频
                                            </el-button>
                                            <el-button
                                                type="danger"
                                                size="small"
                                                @click="deleteProject(project)"
                                                :title="'删除项目'"
                                                class="secondary-button">
                                                <i class="el-icon-delete"></i>
                                                删除
                                            </el-button>
                                        </div>
                                    </div>
                                </el-card>
                            </div>
                        </div>

                        <!-- 分页控制 -->
                        <div class="pagination-container" v-if="projectPagination.totalPages > 1">
                            <el-pagination
                                v-model:current-page="projectPagination.page"
                                v-model:page-size="projectPagination.pageSize"
                                :page-sizes="[10, 20, 50]"
                                :total="projectPagination.total"
                                layout="total, sizes, prev, pager, next, jumper"
                                @size-change="loadProjectList({ pageSize: $event, page: 1 })"
                                @current-change="loadProjectList({ page: $event })">
                            </el-pagination>
                        </div>

                        <!-- 确认选择面板 -->
                        <div v-if="selectedProject" class="confirmation-panel">
                            <el-card shadow="always">
                                <template #header>
                                    <div class="card-header">
                                        <span>确认选择</span>
                                        <el-button type="text" @click="clearProjectSelection()">取消</el-button>
                                    </div>
                                </template>

                                <div class="selected-project-info">
                                    <h4>{{ selectedProject.originalVideoName }}</h4>
                                    <p>项目ID: {{ selectedProject.videoIdentifier }}</p>
                                    <p>文件大小: {{ selectedProject.fileSize }}</p>
                                    <p>上传时间: {{ new Date(selectedProject.uploadTime).toLocaleString() }}</p>
                                    <p>项目状态: {{ selectedProject.projectStatus === 'completed' ? '已完成' :
                                                   selectedProject.projectStatus === 'processed' ? '已处理' :
                                                   selectedProject.projectStatus === 'uploaded' ? '仅上传' : '未知' }}</p>
                                    <p>处理数据: {{ selectedProject.hasProcessedData ? '有' : '无' }}</p>
                                </div>

                                <div class="confirmation-actions">
                                    <el-button type="primary" @click="confirmProjectSelection()">
                                        开始编辑
                                    </el-button>
                                    <el-button @click="clearProjectSelection()">
                                        取消选择
                                    </el-button>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>

                <!--
                  @功能概述: "视频编辑"功能区。
                  @条件渲染: 当 activeSection 的值为 'video-editor' 时显示。
                  @内容: 包含视频播放器和字幕控制。
                -->
                <div v-if="activeSection === 'video-editor' && editorData" class="content-section video-editor-section">
                    <h3>视频编辑与字幕</h3>
                    <h4>{{ editorData.originalVideoName || '视频文件' }}</h4>


                    <!-- 编辑器模式导航 - 显示当前模式状态的导航按钮 -->
                    <div v-if="activeSection === 'video-editor' && !cropDataForBackend.isCroppingActive" class="editor-mode-navigation" style="text-align: center; margin: 35px auto 20px auto; max-width: 960px;">
                        <el-button
                            @click="navigateToSegmentClipping"
                            :type="editorMode === 'segment-clipping' ? 'primary' : 'default'"
                            :disabled="isGenerationCompleted || editorMode === 'segment-clipping'">
                            片段截取 (第一步)
                        </el-button>
                        <el-button
                            @click="navigateToFrameCropping"
                            :type="editorMode === 'frame-cropping' ? 'primary' : 'default'"
                            :disabled="isGenerationCompleted || (clipSegments.length === 0 || clipSegments[clipSegments.length - 1].startTime === null || clipSegments[clipSegments.length - 1].endTime === null) || editorMode === 'frame-cropping'"
                            title="进入画面裁剪模式">
                            画面裁剪 (第二步)
                        </el-button>
                        <el-button
                            @click="navigateToParameterSettings"
                            :type="editorMode === 'parameter-settings' ? 'primary' : 'default'"
                            :disabled="isGenerationCompleted || !isParameterSettingsEnabled || editorMode === 'parameter-settings'"
                            title="进入参数设置模式">
                            参数设置 (第三步)
                        </el-button>
                        <el-button
                            @click="navigateToGeneration"
                            :type="editorMode === 'generation' ? 'primary' : 'default'"
                            :disabled="!isGenerationEnabled || editorMode === 'generation'"
                            title="进入生成确认模式">
                            生成确认 (第四步)
                        </el-button>
                    </div>


                    <!-- 
                      @功能概述: 视频播放器、字幕显示和裁剪画布的容器。
                      @条件渲染: 仅在 'segment-clipping' 或 'frame-cropping' 编辑模式下显示。
                      @内容: 包含视频播放器、用于图像裁剪的Canvas（初始隐藏），以及根据模式条件渲染的字幕显示区。
                      @样式: 采用16:9比例容器。
                    -->

                    <!-- 视频容器 - 在segment-clipping和frame-cropping模式下都显示 -->
                    <div class="video-container-16-9" v-show="editorMode == 'segment-clipping' || editorMode == 'frame-cropping'">
                        <video ref="videoEditorPlayer" :src="editorData.videoPlaybackUrl" type="video/mp4" :controls="editorMode !== 'frame-cropping'" @timeupdate="handleTimeUpdate" @loadedmetadata="onVideoLoaded" v-show="editorMode !== 'frame-cropping'" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"></video>
                        <!-- Subtitle Display Area - 只在非裁剪模式下显示 -->
                        <div v-show="editorMode !== 'frame-cropping'" class="subtitle-display-area video-overlay">
                            <span class="subtitle-line">{{ currentSubtitles || '\u00A0' }}</span>
                        </div>
                    </div>

                    <!-- @功能概述: 多段片段标记时间轴，当 editorMode 为 'segment-clipping' 时显示。 -->
                    <div v-if="editorMode == 'segment-clipping'"
                         class="segment-marker-timeline"
                         style="max-width: 960px; width: 100%; margin: 0px auto 30px auto; height: 12px; background-color: #e9ecef; position: relative; border-radius: 3px; border: 1px solid #ced4da;">

                        <!-- 显示所有片段的选中区域 -->
                        <div v-for="(segmentStyle, index) in selectedSegmentStyles"
                             :key="'segment-' + index"
                             :style="segmentStyle">
                        </div>

                        <!-- 显示所有片段的开始标记 -->
                        <div v-for="(marker, index) in allStartMarkers"
                             :key="'start-marker-' + index"
                             :style="{ position: 'absolute', left: marker.percent + '%', top: 'calc(50% - 6px)', height: '12px', width: '3px', backgroundColor: 'green', zIndex: 2, transform: 'translateX(-1.5px)', cursor: 'pointer' }"
                             @click="() => { if (videoEditorPlayer) videoEditorPlayer.currentTime = marker.time }"
                             :title="'跳转到片段' + (marker.segmentIndex + 1) + '开始时间'">
                        </div>

                        <!-- 显示所有片段的结束标记 -->
                        <div v-for="(marker, index) in allEndMarkers"
                             :key="'end-marker-' + index"
                             :style="{ position: 'absolute', left: marker.percent + '%', top: 'calc(50% - 6px)', height: '12px', width: '3px', backgroundColor: 'orange', zIndex: 2, transform: 'translateX(-1.5px)', cursor: 'pointer' }"
                             @click="() => { if (videoEditorPlayer) videoEditorPlayer.currentTime = marker.time }"
                             :title="'跳转到片段' + (marker.segmentIndex + 1) + '结束时间'">
                        </div>
                    </div>

                    
                    


                    <!-- 片段截取控制：当 editorMode 为 'segment-clipping' 时显示 -->
                    <div v-if="editorMode === 'segment-clipping'" class="segment-clipping-controls" style="margin-bottom: 15px; text-align: center;">
                        <!-- <h4>片段截取模式</h4> -->
                        <el-button-group>


                             <el-button @click="handleSegmentAction"
                                :type="clipStartTime === null ? 'primary' : 'warning'"
                                :disabled="clipEndTime !== null">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="clipStartTime === null ? ElementPlusIconsVue.VideoPlay : ElementPlusIconsVue.VideoPause"></component></el-icon>
                                {{ segmentActionButtonLabel }}
                            </el-button>


                            <el-button @click="resetSegmentSelection"
                                :disabled="clipSegments.length === 0 || (clipSegments.length === 1 && clipSegments[0].startTime === null && clipSegments[0].endTime === null)"
                                :icon="ElementPlusIconsVue.RefreshLeft"
                                title="精细化删除：先删除结束时间，再删除开始时间">
                                删除最后片段
                            </el-button>

                            
                            <el-button @click="seekBackward(3)" :disabled="!videoEditorPlayer" title="将视频倒退3秒">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Back"></component></el-icon>
                                后退3秒
                            </el-button>
                            <el-button @click="seekForward(3)" :disabled="!videoEditorPlayer" title="将视频前进3秒">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Right"></component></el-icon>
                                前进3秒
                            </el-button>
                            <el-button @click="togglePlaybackSpeed" :disabled="!videoEditorPlayer" :title="'当前播放速度: ' + playbackSpeedLabel + '，点击切换'">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Timer"></component></el-icon>
                                {{ playbackSpeedLabel }}
                            </el-button>
                        </el-button-group>
                    </div>




                    <!-- 画面裁剪控制：当 editorMode 为 'frame-cropping' 时显示 -->
                    <div v-if="editorMode === 'frame-cropping'" class="cropping-controls" style="margin-bottom: 15px; text-align: center;">
                        <!-- <h4>画面裁剪模式</h4> -->
                        <el-button-group>
                            <el-button 
                                @click="toggleCropping" 
                                :type="cropDataForBackend.isCroppingActive ? 'danger' : 'primary'"
                                :disabled="isCollectingImages || !isImageCollectionReady"
                                :loading="isCollectingImages">
                                <el-icon v-if="!isCollectingImages" style="vertical-align: middle; margin-right: 4px;"><component :is="cropDataForBackend.isCroppingActive ? ElementPlusIconsVue.CloseBold : ElementPlusIconsVue.Scissor"></component></el-icon>
                                {{ isCollectingImages ? '图像收集中...' : (cropDataForBackend.isCroppingActive ? '关闭画面裁剪' : '启用画面裁剪') }}
                            </el-button>
                            <el-button @click="logCropData" :disabled="!cropDataForBackend.isCroppingActive" type="success">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Check"></component></el-icon>
                                确认裁剪
                            </el-button>
                        </el-button-group>
                        
                        <!-- 状态提示信息 -->
                        <div style="margin-top: 10px;">
                            <!-- 正在收集图像时的提示 -->
                            <p v-if="isCollectingImages" style="color: #E6A23C; font-weight: bold;">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Loading"></component></el-icon>
                                正在收集视频帧图像，请稍候...
                            </p>
                            
                            <!-- 图像收集完成，等待用户启动裁剪 -->
                            <p v-else-if="isImageCollectionReady && !cropDataForBackend.isCroppingActive" style="color: #67C23A;">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.CircleCheckFilled"></component></el-icon>
                                图像切换已准备完成，点击"启用画面裁剪"开始裁剪
                            </p>
                            
                            <!-- 裁剪已激活时的提示 -->
                            <p v-else-if="cropDataForBackend.isCroppingActive" style="color: #409EFF;">
                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Edit"></component></el-icon>
                                裁剪器已激活，可拖动选框调整画面
                            </p>
                            
                            <!-- 图像未准备好时的提示 -->
                            <p v-else style="color: #909399;">
                                图像准备中，请等待...
                            </p>
                        </div>
                    </div>

                    <!-- 参数设置模式 -->
                    <div v-if="editorMode == 'parameter-settings'">
                        <div class="parameter-settings-controls" style="margin-top: 20px; padding: 20px; border: 1px solid #eee; border-radius: 4px; background: #fcfcfc;">
                            <h4 style="margin-top: 0; margin-bottom: 15px; color: #303133; text-align: center;">视频生成参数设置</h4>

                            <!-- 重复次数设置 -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #606266;">重复播放次数 (1-5次):</label>
                                <el-slider
                                    v-model="videoConfig.repeatCount"
                                    :min="1"
                                    :max="5"
                                    :step="1"
                                    show-stops
                                    show-input
                                    @change="updateRepeatCount"
                                    style="margin-bottom: 10px;">
                                </el-slider>
                            </div>

                            <!-- 重复模式设置 -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #606266;">每遍播放模式:</label>
                                <div v-for="(mode, index) in videoConfig.repeatModes" :key="index" style="margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                                    <span style="min-width: 80px; color: #303133;">{{ mode.displayText.split(' ')[0] }}:</span>
                                    <el-select
                                        :model-value="mode.name"
                                        @update:model-value="(value) => updateRepeatMode(index, value)"
                                        style="flex: 1;">
                                        <el-option label="盲听" value="blindListen"></el-option>
                                        <el-option label="单词填空" value="clozedSubtitle"></el-option>
                                        <el-option label="中英翻译" value="bilingualSubtitle"></el-option>
                                    </el-select>
                                </div>
                                
                                <!-- 配置预览 -->
                                <div style="margin-top: 15px; padding: 10px; background: #f5f7fa; border-radius: 4px; border-left: 4px solid #409eff;">
                                    <div style="font-size: 12px; color: #606266; margin-bottom: 5px;">📺 播放序列预览:</div>
                                    <div style="font-size: 13px; color: #303133;">
                                        <span v-for="(mode, index) in videoConfig.repeatModes" :key="index">
                                            {{ mode.displayText }}<span v-if="index < videoConfig.repeatModes.length - 1"> → </span>
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- 重置按钮 -->
                                <div style="margin-top: 15px; text-align: center;">
                                    <el-button 
                                        type="warning" 
                                        size="small" 
                                        @click="resetToDefaultConfig"
                                        :icon="ElementPlusIconsVue.RefreshLeft">
                                        重置为默认配置
                                    </el-button>
                                </div>
                            </div>

                            <!-- 背景风格设置 -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #606266;">🎨 背景风格:</label>
                                <el-select
                                    v-model="videoConfig.backgroundStyle"
                                    @change="updateBackgroundStyle"
                                    style="width: 100%;"
                                    placeholder="请选择背景风格">
                                    <el-option
                                        label="报纸风格 (Newspaper)"
                                        value="newspaper">
                                    </el-option>
                                    <el-option
                                        label="抽象风格 (Abstract)"
                                        value="abstract">
                                    </el-option>
                                </el-select>
                                <div style="margin-top: 8px; font-size: 12px; color: #909399;">
                                    💡 选择视频的背景风格，报纸风格适合新闻类内容，抽象风格更加现代简洁
                                </div>
                            </div>

                            <!-- 视频引导设置 -->
                            <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #e4e7ed; border-radius: 4px; background: #fafafa;">
                                <label style="display: block; margin-bottom: 12px; font-weight: bold; color: #606266; font-size: 14px;">🎯 视频引导设置</label>
                                
                                <!-- 视频引导开关 -->
                                <div style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                                    <label style="min-width: 80px; color: #303133; font-size: 13px;">启用引导:</label>
                                    <el-switch 
                                        v-model="videoConfig.subtitleConfig.videoGuide.enabled"
                                        @change="(value) => {
                                            console.log('[DEBUG][UI][videoGuide.enabled] 开关状态变化:', value);
                                            console.log('[DEBUG][UI][videoGuide.enabled] 更新前的 videoGuide:', JSON.stringify(videoConfig.subtitleConfig.videoGuide, null, 2));
                                            updateVideoConfig();
                                            console.log('[DEBUG][UI][videoGuide.enabled] 更新后的 videoGuide:', JSON.stringify(videoConfig.subtitleConfig.videoGuide, null, 2));
                                        }">
                                    </el-switch>
                                </div>

                                <!-- 视频引导标题设置 -->
                                <div v-if="videoConfig.subtitleConfig.videoGuide.enabled">
                                    <div style="margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                                        <label style="min-width: 80px; color: #303133; font-size: 13px;">标题第一行:</label>
                                        <el-input
                                            v-model="videoConfig.subtitleConfig.videoGuide.title1"
                                            @input="(value) => {
                                                console.log('[DEBUG][UI][videoGuide.title1] 输入值变化:', value);
                                                console.log('[DEBUG][UI][videoGuide.title1] 更新前的 videoGuide:', JSON.stringify(videoConfig.subtitleConfig.videoGuide, null, 2));
                                                updateVideoConfig();
                                                console.log('[DEBUG][UI][videoGuide.title1] 更新后的 videoGuide:', JSON.stringify(videoConfig.subtitleConfig.videoGuide, null, 2));
                                            }"
                                            placeholder="请输入第一行标题"
                                            style="flex: 1;">
                                        </el-input>
                                    </div>
                                    <div style="margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
                                        <label style="min-width: 80px; color: #303133; font-size: 13px;">标题第二行:</label>
                                        <el-input
                                            v-model="videoConfig.subtitleConfig.videoGuide.title2"
                                            @input="(value) => {
                                                console.log('[DEBUG][UI][videoGuide.title2] 输入值变化:', value);
                                                console.log('[DEBUG][UI][videoGuide.title2] 更新前的 videoGuide:', JSON.stringify(videoConfig.subtitleConfig.videoGuide, null, 2));
                                                updateVideoConfig();
                                                console.log('[DEBUG][UI][videoGuide.title2] 更新后的 videoGuide:', JSON.stringify(videoConfig.subtitleConfig.videoGuide, null, 2));
                                            }"
                                            placeholder="请输入第二行标题"
                                            style="flex: 1;">
                                        </el-input>
                                    </div>
                                </div>
                            </div>

                            <!-- 广告设置 -->
                            <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #e4e7ed; border-radius: 4px; background: #fafafa;">
                                <label style="display: block; margin-bottom: 12px; font-weight: bold; color: #606266; font-size: 14px;">📢 广告设置</label>
                                
                                <!-- 广告开关 -->
                                <div style="margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                                    <label style="min-width: 80px; color: #303133; font-size: 13px;">启用广告:</label>
                                    <el-switch 
                                        v-model="videoConfig.subtitleConfig.advertisement.enabled"
                                        @change="(value) => {
                                            console.log('[DEBUG][UI][advertisement.enabled] 开关状态变化:', value);
                                            console.log('[DEBUG][UI][advertisement.enabled] 更新前的 advertisement:', JSON.stringify(videoConfig.subtitleConfig.advertisement, null, 2));
                                            updateVideoConfig();
                                            console.log('[DEBUG][UI][advertisement.enabled] 更新后的 advertisement:', JSON.stringify(videoConfig.subtitleConfig.advertisement, null, 2));
                                        }">
                                    </el-switch>
                                </div>

                                <!-- 广告标题列表 -->
                                <div v-if="videoConfig.subtitleConfig.advertisement.enabled">
                                    <div style="margin-bottom: 10px; display: flex; align-items: center; justify-content: space-between;">
                                        <span style="color: #303133; font-size: 13px; font-weight: bold;">广告标题列表:</span>
                                        <el-button 
                                            type="primary" 
                                            size="small" 
                                            @click="() => {
                                                console.log('[DEBUG][UI][addAdvertisementTitle] 点击添加广告标题按钮');
                                                console.log('[DEBUG][UI][addAdvertisementTitle] 添加前的 advertisement.titles:', JSON.stringify(videoConfig.subtitleConfig.advertisement.titles, null, 2));
                                                addAdvertisementTitle();
                                                console.log('[DEBUG][UI][addAdvertisementTitle] 添加后的 advertisement.titles:', JSON.stringify(videoConfig.subtitleConfig.advertisement.titles, null, 2));
                                            }">
                                            <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Plus"></component></el-icon>
                                            添加标题
                                        </el-button>
                                    </div>

                                    <!-- 广告标题条目 -->
                                    <div v-for="(title, index) in videoConfig.subtitleConfig.advertisement.titles" :key="index" 
                                         style="margin-bottom: 15px; padding: 12px; border: 1px solid #dcdfe6; border-radius: 4px; background: white;">
                                        <div style="margin-bottom: 8px; display: flex; align-items: center; justify-content: space-between;">
                                            <span style="color: #909399; font-size: 12px;">广告标题 {{ index + 1 }}</span>
                                            <el-button 
                                                type="danger" 
                                                size="small" 
                                                @click="() => {
                                                    console.log(`[DEBUG][UI][removeAdvertisementTitle] 点击删除广告标题按钮，索引: ${index}`);
                                                    console.log('[DEBUG][UI][removeAdvertisementTitle] 删除前的 advertisement.titles:', JSON.stringify(videoConfig.subtitleConfig.advertisement.titles, null, 2));
                                                    removeAdvertisementTitle(index);
                                                    console.log('[DEBUG][UI][removeAdvertisementTitle] 删除后的 advertisement.titles:', JSON.stringify(videoConfig.subtitleConfig.advertisement.titles, null, 2));
                                                }"
                                                :disabled="videoConfig.subtitleConfig.advertisement.titles.length <= 1">
                                                <el-icon style="vertical-align: middle; margin-right: 4px;"><component :is="ElementPlusIconsVue.Delete"></component></el-icon>
                                                删除
                                            </el-button>
                                        </div>
                                        <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 10px;">
                                            <label style="min-width: 60px; color: #303133; font-size: 12px;">第一行:</label>
                                            <el-input
                                                v-model="title.line1"
                                                @input="(value) => {
                                                    console.log(`[DEBUG][UI][advertisement.titles[${index}].line1] 输入值变化:`, value);
                                                    console.log(`[DEBUG][UI][advertisement.titles[${index}].line1] 更新前的标题:`, JSON.stringify(title, null, 2));
                                                    updateAdvertisementTitle(index, 'line1', value);
                                                    console.log(`[DEBUG][UI][advertisement.titles[${index}].line1] 更新后的标题:`, JSON.stringify(title, null, 2));
                                                }"
                                                placeholder="请输入第一行内容"
                                                style="flex: 1;"
                                                size="small">
                                            </el-input>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <label style="min-width: 60px; color: #303133; font-size: 12px;">第二行:</label>
                                            <el-input
                                                v-model="title.line2"
                                                @input="(value) => {
                                                    console.log(`[DEBUG][UI][advertisement.titles[${index}].line2] 输入值变化:`, value);
                                                    console.log(`[DEBUG][UI][advertisement.titles[${index}].line2] 更新前的标题:`, JSON.stringify(title, null, 2));
                                                    updateAdvertisementTitle(index, 'line2', value);
                                                    console.log(`[DEBUG][UI][advertisement.titles[${index}].line2] 更新后的标题:`, JSON.stringify(title, null, 2));
                                                }"
                                                placeholder="请输入第二行内容"
                                                style="flex: 1;"
                                                size="small">
                                            </el-input>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 生成确认模式 -->
                    <div v-if="editorMode == 'generation'">
                        <div class="generation-step-controls" style="margin-top: 20px; text-align: center; padding: 20px; border: 1px solid #eee; border-radius: 4px; background: #fcfcfc;">
                            <h4 style="margin-top: 0; margin-bottom: 15px; color: #303133;">最终确认与生成</h4>
                            <p v-if="!isGenerationCompleted && !isGenerationFailed" style="color: #606266; margin-bottom: 20px;">请再次确认下方"关键编辑信息"中的所有参数无误后，点击"开始生成"。</p>
                            <p v-if="isGenerationCompleted" style="color: #67C23A; margin-bottom: 20px;">✅ 视频生成已完成！您可以下载生成的文件，或点击"重新剪辑"开始新的编辑。</p>
                            <p v-if="isGenerationFailed" style="color: #F56C6C; margin-bottom: 20px;">❌ 视频生成失败！请查看下方"实时状态监控"了解详情，或点击"重新剪辑"重新开始。</p>

                            <el-button
                                v-if="!isGenerationCompleted && !isGenerationFailed"
                                type="success"
                                @click="handleStartGeneration"
                                size="large"
                                :disabled="isGenerating"
                                :loading="isGenerating">
                                {{ isGenerating ? '生成中...' : '开始生成' }}
                            </el-button>

                            <el-button
                                v-if="isGenerationCompleted || isGenerationFailed"
                                :type="isGenerationFailed ? 'danger' : 'warning'"
                                @click="handleRestartEditing"
                                size="large">
                                重新剪辑
                            </el-button>
                        </div>
                    </div>

               
                    <!-- 信息展示窗口 -->
                    <div v-if="activeSection === 'video-editor' && editorData" class="info-display-window" style="max-width: 960px; margin: 20px auto; padding: 15px; border: 1px solid #dcdfe6; border-radius: 4px; background-color: #f9fafc; text-align: left;">
                        <h4 style="margin-top: 0; margin-bottom: 10px; color: #303133;">关键编辑信息</h4>
                        
                        <!-- 多段片段选择信息 -->
                        <div style="margin-bottom: 10px;">
                            <strong style="color: #606266;">片段选择数组:</strong>
                            <div style="margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;">
                                <pre>{{ JSON.stringify(clipSegments, null, 2) }}</pre>
                            </div>
                        </div>

                        <!-- 画面裁剪参数 -->
                        <div style="margin-bottom: 10px;">
                            <strong style="color: #606266;">画面裁剪参数:</strong>
                            <div style="margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;">
                                <pre>{{ JSON.stringify(confirmedCropParams, null, 2) }}</pre>
                            </div>
                        </div>

                        <!-- 视频配置参数 -->
                        <div style="margin-bottom: 10px;">
                            <strong style="color: #606266;">视频配置参数:</strong>
                            <div style="margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;">
                                <pre>{{ JSON.stringify(videoConfig, null, 2) }}</pre>
                            </div>
                        </div>

            

                        <!-- 下载链接状态 -->
                        <div style="margin-bottom: 10px;">
                            <strong style="color: #606266;">下载链接状态:</strong>
                            <div style="margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;">
                                <pre>{{ JSON.stringify(downloadLinks, null, 2) }}</pre>
                            </div>
                        </div>
                    </div>

                    <!-- 实时状态监控面板 -->
                    <div v-if="activeSection === 'video-editor' && (sseStatus.connection.isConnected || sseStatus.pipeline.status)" class="info-display-window" style="max-width: 960px; margin: 20px auto; padding: 15px; border: 1px solid #dcdfe6; border-radius: 4px; background-color: #f9fafc; text-align: left;">
                        <h4 style="margin-top: 0; margin-bottom: 10px; color: #303133;">📡 实时状态监控</h4>

                        <!-- SSE连接状态 -->
                        <div style="margin-bottom: 10px;">
                            <strong style="color: #606266;">SSE连接状态:</strong>
                            <div style="margin-top: 10px; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;" :style="{ backgroundColor: sseStatus.connection.isConnected ? '#f0f9ff' : '#fef2f2' }">
                                <pre>连接状态: {{ sseStatus.connection.isConnected ? '✅ 已连接' : '❌ 未连接' }}
连接ID: {{ sseStatus.connection.connectionId || '未设置' }}
开始时间: {{ sseStatus.connection.startTime || '未设置' }}
最后心跳: {{ sseStatus.connection.lastHeartbeat || '未设置' }}
错误次数: {{ sseStatus.connection.errorCount }}</pre>
                            </div>
                        </div>

                        <!-- 流水线状态 -->
                        <div style="margin-bottom: 10px;">
                            <strong style="color: #606266;">流水线状态:</strong>
                            <div style="margin-top: 10px; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;" :style="{ backgroundColor: sseStatus.pipeline.status === 'failed' ? '#fef2f2' : sseStatus.pipeline.status === 'completed' ? '#f0f9ff' : '#fffbeb' }">
                                <pre>流水线名称: {{ sseStatus.pipeline.name || '未设置' }}
状态: {{ getPipelineStatusText(sseStatus.pipeline.status) }}
进度: {{ sseStatus.pipeline.progress }}%
开始时间: {{ sseStatus.pipeline.startTime || '未设置' }}
结束时间: {{ sseStatus.pipeline.endTime || '进行中' }}</pre>
                                <div v-if="sseStatus.pipeline.errorDetails" style="margin-top: 10px; padding: 8px; background-color: #fee; border-left: 3px solid #f56565; color: #c53030;">
                                    <strong>错误详情:</strong><br>
                                    {{ JSON.stringify(sseStatus.pipeline.errorDetails, null, 2) }}
                                </div>
                            </div>
                        </div>

                        <!-- 当前任务状态 -->
                        <div v-if="sseStatus.currentTask.name" style="margin-bottom: 10px;">
                            <strong style="color: #606266;">当前任务:</strong>
                            <div style="margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;">
                                <pre>任务名称: {{ sseStatus.currentTask.name }}
任务状态: {{ getTaskStatusText(sseStatus.currentTask.status) }}
详细信息: {{ sseStatus.currentTask.detail || '无' }}
开始时间: {{ sseStatus.currentTask.startTime || '未设置' }}</pre>
                                <div v-if="sseStatus.currentTask.errorInfo" style="margin-top: 10px; padding: 8px; background-color: #fee; border-left: 3px solid #f56565; color: #c53030;">
                                    <strong>任务错误:</strong><br>
                                    {{ JSON.stringify(sseStatus.currentTask.errorInfo, null, 2) }}
                                </div>
                            </div>
                        </div>

                        <!-- 任务历史记录 -->
                        <div v-if="sseStatus.taskHistory.length > 0" style="margin-bottom: 10px;">
                            <strong style="color: #606266;">任务历史记录:</strong>
                            <div style="margin-top: 10px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px; color: #333;">
                                <div v-for="(task, index) in sseStatus.taskHistory" :key="index" style="margin-bottom: 8px; padding: 6px; border-radius: 3px;" :style="{ backgroundColor: task.status === 'failed' ? '#fee' : task.status === 'completed' ? '#f0fff4' : '#fff' }">
                                    <strong>{{ task.name }}</strong> - {{ getTaskStatusText(task.status) }}
                                    <div v-if="task.errorSummary" style="color: #c53030; font-size: 11px;">错误: {{ task.errorSummary }}</div>
                                    <div v-if="task.resultPreview" style="color: #38a169; font-size: 11px;">结果: {{ task.resultPreview }}</div>
                                </div>
                            </div>
                        </div>
                    </div>



                </div>


                <!-- 没有可编辑的视频数据。请先上传并处理一个视频。 -->
                <div v-if="activeSection === 'video-editor' && !editorData" class="content-section">
                    <p>没有可编辑的视频数据。请先上传并处理一个视频。</p>
                </div>



                <!-- 下载链接区域 -->
                <div v-if="downloadLinks.enhancedBilingualSubtitle || downloadLinks.finalVideo" class="download-section">
                    <h3 class="download-title">📥 生成文件下载</h3>
                    
                    <!-- 调试信息 -->
                    <div style="margin-bottom: 15px; padding: 10px; background: #f0f0f0; border-radius: 4px; font-size: 12px; color: #666;">
                        <strong>调试信息:</strong><br>
                        字幕链接: {{ downloadLinks.enhancedBilingualSubtitle || '未设置' }}<br>
                        视频链接: {{ downloadLinks.finalVideo || '未设置' }}
                    </div>
                    
                    <div class="download-links">
                        <!-- 增强双语字幕下载 -->
                        <div v-if="downloadLinks.enhancedBilingualSubtitle" class="download-item">
                            <div class="download-info">
                                <span class="download-icon">📝</span>
                                <span class="download-label">增强双语字幕文件</span>
                                <span class="download-path">{{ downloadLinks.enhancedBilingualSubtitle }}</span>
                            </div>
                            <a :href="'/api/download?file=' + encodeURIComponent(downloadLinks.enhancedBilingualSubtitle)"
                               download
                               class="download-button">
                                <el-button type="primary" size="small">
                                    <el-icon style="vertical-align: middle; margin-right: 8px;"><component :is="ElementPlusIconsVue.Download"></component></el-icon>
                                    下载字幕
                                </el-button>
                            </a>
                        </div>

                        <!-- 最终视频下载 -->
                        <div v-if="downloadLinks.finalVideo" class="download-item">
                            <div class="download-info">
                                <span class="download-icon">🎬</span>
                                <span class="download-label">最终视频文件</span>
                                <span class="download-path">{{ downloadLinks.finalVideo }}</span>
                            </div>
                            <a :href="'/api/download?file=' + encodeURIComponent(downloadLinks.finalVideo)"
                               download
                               class="download-button">
                                <el-button type="success" size="small">
                                    <el-icon style="vertical-align: middle; margin-right: 8px;"><component :is="ElementPlusIconsVue.Download"></component></el-icon>
                                    下载视频
                                </el-button>
                            </a>
                        </div>
                    </div>
                </div>


                <!--
                  @功能概述: "我的视频"功能区（占位）。
                  @条件渲染: 当 activeSection 的值为 'my-videos' 时显示。
                  @内容: 当前仅为占位文本。
                -->
                <!--
                <div v-if="activeSection === 'my-videos'" class="content-section">
                    我的视频列表区 (占位)
                </div>
                -->

        
            </el-main>
        </el-container>

        <!-- 生成视频查看对话框 -->
        <el-dialog
            v-model="showGeneratedVideosDialog"
            title="生成的短视频"
            width="800px"
            :modal="true"
            :append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="true"
            :show-close="true"
            :before-close="closeGeneratedVideosDialog">

            <!-- 加载状态 -->
            <div v-if="loadingGeneratedVideos" style="text-align: center; padding: 40px;">
                <div class="loading-spinner" style="font-size: 24px; margin-bottom: 16px;">
                    ⏳
                </div>
                <p>正在加载生成的视频...</p>
            </div>

            <!-- 视频列表 -->
            <div v-else-if="generatedVideosList.length > 0">
                <!-- 全选控制 -->
                <div style="margin-bottom: 16px; padding: 12px; background-color: #f5f7fa; border-radius: 6px;">
                    <el-checkbox
                        v-model="selectAllVideos"
                        @change="toggleSelectAll">
                        全选 ({{selectedVideos.size}}/{{generatedVideosList.length}})
                    </el-checkbox>
                    <span style="margin-left: 16px; color: #909399; font-size: 12px;">
                        已选中 {{selectedVideos.size}} 个视频
                    </span>
                </div>

                <!-- 视频列表 -->
                <div class="video-list">
                    <div
                        v-for="pair in generatedVideosList"
                        :key="pair.video.filename"
                        class="video-item">
                        <el-checkbox
                            :model-value="selectedVideos.has(pair.video.filename)"
                            @update:model-value="(value) => toggleVideoSelection(pair.video.filename)">
                        </el-checkbox>

                        <div class="video-info">
                            <h4>{{pair.videoIdentifier}}</h4>
                            <div class="video-details">
                                <span class="detail-item">
                                    📹 大小: {{pair.video.size}}
                                </span>
                                <span class="detail-item">
                                    ⏱️ 时长: {{pair.video.duration}}
                                </span>
                                <span class="detail-item">
                                    📅 创建: {{Utils.formatTimestamp(pair.video.timestamp)}}
                                </span>
                            </div>
                            <div class="video-tags-info">
                                <div v-if="pair.subtitle" class="subtitle-info">
                                    <el-tag size="small" type="success">
                                        📄 已配对字幕
                                    </el-tag>
                                </div>
                                <div v-else class="subtitle-info">
                                    <el-tag size="small" type="warning">
                                        ⚠️ 无字幕文件
                                    </el-tag>
                                </div>
                                <div class="status-info">
                                    <el-tag
                                        size="small"
                                        :type="getVideoStatusType(pair.video.filename)">
                                        {{getVideoStatusText(pair.video.filename)}}
                                    </el-tag>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else style="text-align: center; padding: 40px;">
                <div style="font-size: 48px; color: #c0c4cc; margin-bottom: 16px;">
                    📹
                </div>
                <p style="color: #909399; margin: 0;">该项目还没有生成短视频</p>
                <p style="color: #c0c4cc; font-size: 12px; margin: 8px 0 0 0;">
                    请先使用视频生成功能创建短视频
                </p>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span v-if="selectedVideos.size > 0" style="color: #409eff; font-size: 14px;">
                        已选中 {{selectedVideos.size}} 个视频，可进行批量操作
                    </span>
                    <div>
                        <el-button @click="closeGeneratedVideosDialog">关闭</el-button>
                        <el-button
                            v-if="selectedVideos.size > 0"
                            type="success"
                            @click="markSelectedAsPublished"
                            :loading="markingAsPublished">
                            标注已发布 ({{selectedVideos.size}})
                        </el-button>
                        <el-button
                            v-if="selectedVideos.size > 0"
                            type="primary"
                            @click="batchDownloadSelected"
                            :loading="batchDownloading">
                            批量下载 ({{selectedVideos.size}})
                        </el-button>
                        <el-button
                            v-if="selectedVideos.size > 0"
                            type="warning"
                            @click="generateLiveVideoDraft"
                            :loading="generatingLiveVideoDraft">
                            生成直播视频草稿 ({{selectedVideos.size}})
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>

        <!-- 项目选择确认对话框 -->
        <el-dialog
            v-model="showProjectSelectionDialog"
            title="确认项目选择"
            width="600px"
            :close-on-click-modal="false"
            :close-on-press-escape="true"
            @close="clearProjectSelection">

            <div v-if="dialogSelectedProject" class="project-selection-content">
                <!-- 项目信息展示 -->
                <div class="selected-project-info">
                    <h3>{{ dialogSelectedProject.originalVideoName }}</h3>

                    <div class="project-details">
                        <div class="detail-row">
                            <span class="label">项目ID:</span>
                            <span class="value">{{ dialogSelectedProject.videoIdentifier }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">文件大小:</span>
                            <span class="value">{{ dialogSelectedProject.fileSize }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">上传时间:</span>
                            <span class="value">{{ new Date(dialogSelectedProject.uploadTime).toLocaleString() }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">项目状态:</span>
                            <el-tag
                                :type="dialogSelectedProject.projectStatus === 'completed' ? 'success' :
                                       dialogSelectedProject.projectStatus === 'processed' ? 'warning' :
                                       dialogSelectedProject.projectStatus === 'uploaded' ? 'info' : 'danger'"
                                size="small">
                                {{ dialogSelectedProject.projectStatus === 'completed' ? '已完成' :
                                   dialogSelectedProject.projectStatus === 'processed' ? '已处理' :
                                   dialogSelectedProject.projectStatus === 'uploaded' ? '仅上传' : '未知' }}
                            </el-tag>
                        </div>
                        <div class="detail-row">
                            <span class="label">处理数据:</span>
                            <span class="value">{{ dialogSelectedProject.hasProcessedData ? '有' : '无' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 确认提示 -->
                <div class="confirmation-message">
                    <el-alert
                        title="确认选择此项目进行编辑？"
                        type="info"
                        :closable="false"
                        show-icon>
                        <template #default>
                            选择后将进入视频编辑器，开始对该项目进行编辑操作。
                        </template>
                    </el-alert>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="clearProjectSelection">取消</el-button>
                    <el-button
                        type="primary"
                        @click="confirmProjectSelection"
                        :disabled="!dialogSelectedProject">
                        开始编辑
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <!-- Cropper.js JS: 更改为 v1.5.13 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>

    <!-- JSZip for batch download -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <!-- 应用程序核心脚本 - 按照前端开发规范的加载顺序 -->
    <!-- 1. 工具函数库（无依赖） -->
    <script src="./js/utils.js"></script>
    <!-- 2. SSE事件管理器（依赖utils） -->
    <script src="./js/sse-event-manager.js"></script>
    <!-- 3. 视频编辑器模块（依赖utils） -->
    <script src="./js/video-editor.js"></script>
    <!-- 4. 应用主入口（依赖前面所有模块） -->
    <script src="./js/app.js"></script>

    <!-- 应用启动脚本 -->
    <script defer>
        document.addEventListener('DOMContentLoaded', () => {
            const logPrefix = '[文件：index.html][应用启动]';
            console.log(`${logPrefix} DOM内容已加载，准备启动Vue应用。`);
            
            // 确保AppCore和startApp函数已定义
            if (window.AppCore && typeof window.AppCore.startApp === 'function') {
                try {
                    window.AppCore.startApp();
                    console.log(`${logPrefix} Vue应用启动函数已调用。`);
                } catch (error) {
                    console.error(`${logPrefix} 调用startApp时出错:`, error);
                }
            } else {
                console.error(`${logPrefix} Vue应用启动失败: Error: AppCore或startApp函数未在全局范围正确定义。`);
                // 可以在页面上显示错误信息
                const appElement = document.getElementById('app');
                if (appElement) {
                    appElement.innerHTML = '<div style="color: red; padding: 20px;">应用核心脚本加载失败，请检查浏览器控制台。</div>';
                }
            }
        });
    </script>
</body>
</html>
