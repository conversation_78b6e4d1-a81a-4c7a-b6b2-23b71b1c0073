/**
 * RemotionTSXGeneratorTask 专属测试文件
 * 按照任务开发标准创建的自包含测试文件
 * 可通过 node backend/src/tasks/tests/RemotionTSXGeneratorTask.test.js 直接执行
 */

const RemotionTSXGeneratorTask = require('../RemotionTSXGeneratorTask');
const logger = require('../../utils/logger');
const { TASK_STATUS, TASK_SUBSTATUS } = require('../../constants/progress');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 加载真实的视频配置文件
const videoConfigPath = path.resolve(__dirname, '../../config/video/video-config.json');
const realVideoConfig = JSON.parse(fs.readFileSync(videoConfigPath, 'utf8'));

// 真实的媒体文件路径
const realVideoPath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612.mp4';
const realAudioPath = 'C:\\Users\\<USER>\\Desktop\\codebase\\express\\backend\\uploads\\input\\test_0612_audio.mp3';

// 获取音频时长的函数
function getAudioDuration(audioPath) {
    return new Promise((resolve, reject) => {
        const ffprobe = spawn('ffprobe', [
            '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            audioPath
        ]);
        
        let output = '';
        ffprobe.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        ffprobe.on('close', (code) => {
            if (code === 0) {
                const duration = parseFloat(output.trim());
                resolve(duration);
            } else {
                // 如果ffprobe失败，使用默认值
                logger.warn(`${testLogPrefix} 无法获取音频时长，使用默认值 30 秒`);
                resolve(30);
            }
        });
        
        ffprobe.on('error', (error) => {
            logger.warn(`${testLogPrefix} ffprobe执行失败: ${error.message}，使用默认值 30 秒`);
            resolve(30);
        });
    });
}

// 测试日志前缀
const testLogPrefix = '[文件：RemotionTSXGeneratorTask.test.js][RemotionTSX生成任务测试]';

// 断言函数
function assert(condition, message) {
    if (!condition) {
        logger.error(`${testLogPrefix}[断言失败] ${message}`);
        throw new Error(`断言失败: ${message}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message}`);
}

function assertEquals(actual, expected, message) {
    if (actual !== expected) {
        const fullMessage = `${message} - 期望: ${expected}, 实际: ${actual}`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (值: ${actual})`);
}

function assertIncludes(arrayOrString, substring, message) {
    if (!arrayOrString || !arrayOrString.includes(substring)) {
        const fullMessage = `${message} - 期望包含: "${substring}", 实际: "${arrayOrString}"`;
        logger.error(`${testLogPrefix}[断言失败] ${fullMessage}`);
        throw new Error(`断言失败: ${fullMessage}`);
    }
    logger.info(`${testLogPrefix}[断言成功] ${message} (包含: "${substring}")`);
}

async function runTests() {
    logger.info(`${testLogPrefix} ========== 开始执行 RemotionTSXGeneratorTask 测试 ==========`);
    
    // 获取真实的音频时长
    logger.info(`${testLogPrefix} 正在获取音频文件时长: ${realAudioPath}`);
    const realAudioDuration = await getAudioDuration(realAudioPath);
    logger.info(`${testLogPrefix} 音频时长: ${realAudioDuration} 秒`);
    
    let testsPassed = 0;
    let testsFailed = 0;

    const runSingleTest = async (testName, testFn) => {
        logger.info(`${testLogPrefix} --- 测试用例开始: ${testName} ---`);
        try {
            await testFn();
            logger.info(`${testLogPrefix} --- ✅ 测试用例通过: ${testName} ---`);
            testsPassed++;
        } catch (error) {
            logger.error(`${testLogPrefix} --- ❌ 测试用例失败: ${testName} ---`);
            logger.error(`${testLogPrefix} 错误详情: ${error.message}`);
            if (error.stack) {
                logger.error(`${testLogPrefix} 堆栈: ${error.stack}`);
            }
            testsFailed++;
        }
        logger.info(''); // 添加空行以分隔测试用例日志
    };

    // --- 测试用例定义区 ---

    await runSingleTest('1. 任务实例化', async () => {
        const task = new RemotionTSXGeneratorTask();
        assert(task instanceof RemotionTSXGeneratorTask, '任务应为 RemotionTSXGeneratorTask 的实例');
        assertEquals(task.name, 'RemotionTSXGeneratorTask', '任务名称应为 RemotionTSXGeneratorTask');
        assertEquals(task.status, TASK_STATUS.PENDING, '任务初始状态应为 PENDING');
    });

    await runSingleTest('2. 缺少必需字段 - videoConfig', async () => {
        const task = new RemotionTSXGeneratorTask();
        const context = { 
            reqId: 'test-missing-videoconfig',
            // 缺少 videoConfig，但提供其他必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        const progressLogs = [];
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            assertIncludes(error.message, '缺少必需字段', '错误消息应指明缺少字段');
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
            const hasFailedProgress = progressLogs.some(p => p.status === TASK_STATUS.FAILED);
            assert(hasFailedProgress, '应记录 FAILED 状态的进度回调');
        }
    });

    await runSingleTest('3. 微模板Schema验证 - 新标准格式', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        // 使用真实的视频配置文件，并添加remotionTemplate配置
        const context = {
            reqId: 'test-schema-validation',
            videoConfig: realVideoConfig,
            remotionTemplate: {
                layers: [
                    {
                        type: 'background',
                        template: 'newspaper'
                    },
                    {
                        type: 'video',
                        template: 'default-muted'
                    }
                ]
            },
            // 提供所有必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });
        
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assert(result.compositionId, '结果应包含组合ID');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        
        const hasStartedProgress = progressLogs.some(p => p.status === TASK_STATUS.STARTED);
        const hasCompletedProgress = progressLogs.some(p => p.status === TASK_STATUS.COMPLETED);
        assert(hasStartedProgress, '应记录 STARTED 状态的进度回调');
        assert(hasCompletedProgress, '应记录 COMPLETED 状态的进度回调');
    });

    await runSingleTest('4. 向后兼容性测试 - 新旧格式混合', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        const context = {
            reqId: 'test-backward-compatibility',
            videoConfig: realVideoConfig,
            // 不提供remotionTemplate，测试向后兼容性
            // 提供所有必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });
        
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assert(result.compositionId, '结果应包含组合ID');
        assert(result.totalAudioDuration, '结果应包含总音频时长');
        assert(result.copiedAudioPath, '结果应包含复制的音频路径');
        assert(result.copiedVideoPath, '结果应包含复制的视频路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
    });

    await runSingleTest('5. 错误处理 - 不存在的微模板文件', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        const context = {
            reqId: 'test-missing-template',
            videoConfig: realVideoConfig,
            remotionTemplate: {
                layers: [
                    {
                        type: 'background',
                        template: 'nonexistent-template'
                    }
                ]
            },
            // 提供所有必需字段
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        try {
            await task.execute(context, (data) => progressLogs.push(data));
            throw new Error('预期抛出错误但未抛出');
        } catch (error) {
            // 检查错误消息是否包含文件不存在的相关信息
            const errorMessage = error.message.toLowerCase();
            const hasFileNotFoundError = errorMessage.includes('enoent') || 
                                       errorMessage.includes('no such file') || 
                                       errorMessage.includes('cannot find') ||
                                       errorMessage.includes('not found');
            assert(hasFileNotFoundError, `错误消息应指明文件不存在，实际错误: ${error.message}`);
            assertEquals(task.status, TASK_STATUS.FAILED, '任务状态应为 FAILED');
        }
    });

    await runSingleTest('6. VideoGuide功能测试', async () => {
        const task = new RemotionTSXGeneratorTask();
        
        // 直接使用真实配置文件中的videoGuide配置
        const context = {
            reqId: 'test-videoguide',
            videoConfig: realVideoConfig, // 使用真实配置，不覆盖videoGuide
            originalVideoPath: realVideoPath,
            audioFilePath: realAudioPath,
            audioDuration: realAudioDuration
        };
        
        const progressLogs = [];
        
        const result = await task.execute(context, (data) => {
            logger.debug(`${testLogPrefix}[进度回调]: ${JSON.stringify(data)}`);
            progressLogs.push(data);
        });
        
        assert(result, '任务执行应返回结果');
        assert(result.generatedTSXPath, '结果应包含生成的TSX文件路径');
        assertEquals(task.status, TASK_STATUS.COMPLETED, '任务状态应为 COMPLETED');
        
        // 检查生成的TSX文件是否包含videoGuide内容（使用真实配置中的值）
        const tsxContent = fs.readFileSync(result.generatedTSXPath, 'utf8');
        assertIncludes(tsxContent, '坚持30天', 'TSX文件应包含title1文本');
        assertIncludes(tsxContent, '听懂国外新闻', 'TSX文件应包含title2文本');
        assertIncludes(tsxContent, '"fontSize":"70px"', 'TSX文件应包含innerStyle中的fontSize样式');
        assertIncludes(tsxContent, '"color":"rgb(255, 255, 255)"', 'TSX文件应包含转换后的color样式');
        assertIncludes(tsxContent, '"fontWeight":"bold"', 'TSX文件应包含转换后的fontWeight样式');
        assertIncludes(tsxContent, '"textShadow":', 'TSX文件应包含转换后的textShadow样式');
        assertIncludes(tsxContent, '"top":"330px"', 'TSX文件应包含title1的top位置');
        assertIncludes(tsxContent, '"top":"460px"', 'TSX文件应包含title2的top位置');
        assertIncludes(tsxContent, '"padding":"5px 20px 20px"', 'TSX文件应包含title2的padding样式');
        
        logger.info(`${testLogPrefix} VideoGuide功能测试通过，生成的TSX文件: ${result.generatedTSXPath}`);
    });

    // --- 测试结果汇总 ---
    logger.info(`${testLogPrefix} ========== 测试执行完毕 ==========`);
    logger.info(`${testLogPrefix} 通过: ${testsPassed} 个测试`);
    logger.info(`${testLogPrefix} 失败: ${testsFailed} 个测试`);
    
    if (testsFailed > 0) {
        logger.error(`${testLogPrefix} 存在失败的测试用例，请检查上述错误信息`);
        process.exit(1);
    } else {
        logger.info(`${testLogPrefix} 🎉 所有测试用例均通过！`);
        process.exit(0);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runTests().catch(error => {
        logger.error(`${testLogPrefix} 测试执行过程中发生未捕获错误: ${error.message}`);
        logger.error(`${testLogPrefix} 错误堆栈: ${error.stack}`);
        process.exit(1);
    });
}

module.exports = { runTests };
