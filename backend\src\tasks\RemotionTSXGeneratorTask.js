/**
 * @功能概述: Remotion TSX文件生成任务，基于渐进式"微模板"模式动态生成TSX组件文件
 * @架构模式: 渐进式配置迁移 - 优先使用remotionTemplate新配置体系，回退到videoConfig旧配置体系
 * @核心能力:
 *   - 配置体系迁移: 支持新旧配置体系的平滑过渡，优先处理remotionTemplate.layers指令集
 *   - 图层化编排: 遍历layers数组，加载templates目录下的微模板JSON文件
 *   - 视频图层处理: 支持将16:9视频作为图层叠加到9:16背景上
 *   - 音频处理: 支持音频重复拼接以匹配视频时长，使用FFmpeg进行无缝拼接
 *   - 动态配置: 从JSON微模板加载背景、视频等组件配置
 *   - TSX生成: 动态创建可被Remotion渲染的React组件文件，调用components目录下的组件
 * @输入依赖 (Context Input):
 *   必需字段:
 *   - context.videoConfig: {Object} 视频配置对象 (旧配置体系，向后兼容)
 *   - context.originalVideoPath: {string} 16:9原始视频文件路径
 *   - context.audioFilePath: {string} 原始音频文件路径
 *   - context.audioDuration: {number} 原始音频时长（秒）
 *   可选字段:
 *   - context.remotionTemplate: {Object} 新配置体系，包含layers数组指令集
 *   - context.templateConfig: {Object} 模板配置对象 (如果不提供，将使用默认模板)
 * @输出结果: 向context添加generatedTSXPath, copiedAudioPath, copiedVideoPath, totalAudioDuration等字段
 * @外部依赖: 文件系统操作、FFmpeg音频处理、templates目录微模板文件、components目录React组件
 * @失败策略: 生成失败时抛出详细错误信息，包含具体的失败步骤和原因
 * @架构职责: 作为编排层，将配置指令转换为具体的TSX代码，连接配置层和视图层
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const TaskBase = require('../class/TaskBase');
const logger = require('../utils/logger');
const Ajv = require('ajv');
const { microTemplateSchema } = require('../remotion/schemas/microTemplateSchema');
const { TASK_STATUS } = require('../constants/progress');

const taskModuleLogPrefix = '[文件：RemotionTSXGeneratorTask.js][TSX生成任务][模块初始化]';
logger.info(`${taskModuleLogPrefix}模块已加载。`); // 日志：记录模块加载完成

/**
 * @功能概述: Remotion TSX生成任务类，继承自TaskBase，实现渐进式配置迁移的TSX文件生成
 * @架构定位: 编排层核心组件，负责将配置指令转换为可执行的TSX代码
 * @设计模式: 渐进式迁移模式，支持新旧配置体系的平滑过渡
 */
class RemotionTSXGeneratorTask extends TaskBase {
    /**
     * @功能概述: 构造函数，初始化TSX生成任务实例和目录路径配置
     * @param {string} reqId - 请求ID，用于日志追踪和上下文关联
     * @执行流程:
     *   1. 调用父类构造函数初始化TaskBase基础功能
     *   2. 设置请求ID和日志前缀，确保日志可追踪
     *   3. 初始化Remotion架构相关的目录路径
     *   4. 记录实例创建成功日志
     */
    constructor(reqId = 'unknown_req') {
        super('RemotionTSXGeneratorTask'); // 调用TaskBase构造函数，设置任务名称
        this.reqId = reqId; // 设置请求ID，用于日志追踪
        this.instanceLogPrefix = `[文件：RemotionTSXGeneratorTask.js][TSX生成任务][${this.name}]`; // 构建实例级日志前缀

        // 初始化Remotion架构目录路径，严格遵循架构职责划分
        this.generatedDir = path.resolve(__dirname, '../remotion/generated'); // 生成物目录：存放自动创建的TSX文件
        this.componentsDir = path.resolve(__dirname, '../remotion/components'); // 组件库目录：存放可重用的React组件
        this.templatesDir = path.resolve(__dirname, '../remotion/templates'); // 配置库目录：存放微模板JSON文件
        this.publicDir = path.resolve(__dirname, '../remotion/public'); // 资源库目录：存放静态资源文件

        // 初始化JSON Schema验证器
        this.ajv = new Ajv({ allErrors: true });
        this.validateMicroTemplate = this.ajv.compile(microTemplateSchema);

        logger.info(`${this.instanceLogPrefix} 实例已创建。`); // 日志：记录任务实例创建成功
    }

    /**
     * @功能概述: 执行TSX文件生成任务，实现渐进式配置迁移和多媒体处理
     * @param {Object} context - 任务上下文，包含配置和媒体文件路径
     * @param {Function} progressCallback - 进度回调函数，用于实时报告任务进度
     * @returns {Promise<Object>} 任务执行结果，包含生成的文件路径和配置信息
     * @执行流程:
     *   1. 参数校验与准备：验证必需字段，提取核心配置参数
     *   2. 时长计算：基于repeatCount计算总音频时长和单次播放时长
     *   3. 媒体处理：并行复制音频和视频文件到public目录，进行重复拼接
     *   4. 配置转换：核心逻辑，实现新旧配置体系的渐进式迁移
     *   5. 帧数计算：根据音频时长和帧率计算视频总帧数
     *   6. TSX生成：基于最终配置生成React组件代码
     *   7. 文件写入：将生成的TSX内容写入generated目录
     *   8. 根组件更新：更新Root.tsx以注册新的Composition
     */
    async execute(context, progressCallback) {
        const execLogPrefix = `${this.instanceLogPrefix}[execute][ReqID:${this.reqId}]`;

        this.setProgressCallback(progressCallback); // 设置进度回调函数
        this.start(); // 启动任务，记录开始时间和状态

        try {
            // 步骤 1: 参数校验与准备
            // 验证必需字段，确保任务执行的前置条件满足
            const requiredFields = ['videoConfig', 'originalVideoPath', 'audioFilePath', 'audioDuration'];
            this.validateRequiredFields(context, requiredFields, execLogPrefix); // 调用TaskBase的字段验证方法
            const { videoConfig, originalVideoPath, audioFilePath, audioDuration } = context; // 解构提取核心参数
            logger.debug(`${execLogPrefix}[步骤 1] 参数校验通过，音频时长: ${audioDuration}秒`); // 日志：记录参数校验成功

            // 步骤 2: 时长计算
            // 基于repeatCount配置计算总音频时长，支持教育视频的"听三遍"模式
            const repeatCount = videoConfig.repeatCount || 3; // 默认重复3次
            const totalAudioDuration = audioDuration * repeatCount; // 计算总音频时长
            const singlePlayDuration = audioDuration; // 保存单次播放时长，用于视频图层计算
            logger.info(`${execLogPrefix}[步骤 2] 时长计算完成，单次: ${singlePlayDuration}秒，总计: ${totalAudioDuration}秒（重复${repeatCount}次）`); // 日志：记录时长计算结果

            // 步骤 3: 媒体处理 (并行)
            // 并行处理音频和视频文件，提高执行效率
            logger.info(`${execLogPrefix}[步骤 3] 开始并行处理媒体文件`); // 日志：记录媒体处理开始
            const [copiedAudioPath, copiedVideoPath] = await Promise.all([
                this._copyAudioToPublic(audioFilePath, repeatCount), // 复制并重复拼接音频文件
                this._copyVideoToPublic(originalVideoPath) // 复制视频文件到public目录
            ]);
            logger.info(`${execLogPrefix}[步骤 3] 媒体文件处理完成，音频: ${copiedAudioPath}，视频: ${copiedVideoPath}`); // 日志：记录媒体处理完成

            // 步骤 4: 配置转换 (核心逻辑)
            // 实现渐进式配置迁移：优先使用remotionTemplate，回退到videoConfig
            logger.info(`${execLogPrefix}[步骤 4] 开始配置转换，实现渐进式配置迁移`); // 日志：记录配置转换开始
            const finalTemplate = await this._convertVideoConfigToTemplate(context, copiedAudioPath, copiedVideoPath);
            logger.info(`${execLogPrefix}[步骤 4] 配置转换完成，最终模板ID: ${finalTemplate.composition.id}`); // 日志：记录配置转换完成

            // 步骤 5: 帧数计算
            // 根据音频时长和帧率计算视频总帧数，确保音视频同步
            const fps = finalTemplate.composition.fps; // 获取帧率配置
            const totalDurationInFrames = Math.round(totalAudioDuration * fps); // 计算总帧数
            const singlePlayDurationInFrames = Math.round(singlePlayDuration * fps); // 计算单次播放帧数
            logger.info(`${execLogPrefix}[步骤 5] 帧数计算完成，总帧数: ${totalDurationInFrames}帧，单次: ${singlePlayDurationInFrames}帧（@${fps}fps）`); // 日志：记录帧数计算结果

            // 步骤 6: TSX生成
            // 基于最终配置生成React组件代码，调用components目录下的组件
            logger.info(`${execLogPrefix}[步骤 6] 开始生成TSX内容`); // 日志：记录TSX生成开始
            const tsxContent = this._generateTSXContent(finalTemplate, singlePlayDurationInFrames, totalDurationInFrames);
            logger.debug(`${execLogPrefix}[步骤 6] TSX内容生成完成，长度: ${tsxContent.length}字符`); // 日志：记录TSX生成完成

            // 步骤 7: 文件写入
            // 将生成的TSX内容写入generated目录，使用时间戳确保文件名唯一
            const timestamp = Date.now(); // 生成时间戳
            const fileName = `VideoComponent_${timestamp}.tsx`; // 构建文件名
            const filePath = path.join(this.generatedDir, fileName); // 构建完整文件路径
            await fs.writeFile(filePath, tsxContent, 'utf8'); // 写入TSX文件
            logger.info(`${execLogPrefix}[步骤 7] TSX文件写入完成: ${filePath}`); // 日志：记录文件写入成功

            // 步骤 8: 根组件更新
            // 更新Root.tsx以注册新的Composition，使其可在Remotion Studio中预览
            logger.info(`${execLogPrefix}[步骤 8] 开始更新Root.tsx`); // 日志：记录根组件更新开始
            await this._updateRootTSX(finalTemplate, fileName, totalDurationInFrames);
            logger.info(`${execLogPrefix}[步骤 8] Root.tsx更新完成`); // 日志：记录根组件更新完成

            // 构建任务执行结果
            const taskResult = {
                generatedTSXPath: filePath, // 生成的TSX文件路径
                compositionId: finalTemplate.composition.id, // Composition ID
                totalAudioDuration, // 总音频时长
                totalDurationInFrames, // 总帧数
                copiedAudioPath, // 复制的音频文件路径
                copiedVideoPath, // 复制的视频文件路径
                originalAudioDuration: audioDuration, // 原始音频时长
                repeatCount // 重复次数
            };

            this.complete(taskResult); // 标记任务完成，保存结果
            logger.info(`${execLogPrefix} 任务执行成功。`); // 日志：记录任务执行成功
            return taskResult; // 返回任务结果

        } catch (error) {
            // 错误处理：记录详细错误信息并标记任务失败
            logger.error(`${execLogPrefix}[ERROR] 任务执行失败: ${error.message}`, error); // 日志：记录错误详情和堆栈
            this.fail(error); // 调用TaskBase的失败处理方法，更新任务状态
            throw error; // 重新抛出错误，供上层流水线处理
        }
    }

    /**
     * @功能概述: 生成TSX内容，基于图层配置动态构建React组件代码
     * @param {Object} templateConfig - 最终模板配置对象，包含composition、layers、audio等
     * @param {number} singlePlayDurationInFrames - 单次播放时长（帧数），用于视频图层的Sequence配置
     * @param {number} totalDurationInFrames - 总时长（帧数），用于textArea图层的时序控制
     * @returns {string} 生成的TSX代码字符串
     * @私有方法: TSX代码生成核心逻辑
     * @执行流程:
     *   1. 提取配置参数：从templateConfig中解构composition、layers、audio配置
     *   2. 初始化导入和渲染数组：准备imports集合和layerRenders数组
     *   3. 遍历图层配置：为每个layer生成对应的React组件代码
     *   4. 处理组件导入：动态添加components目录下的组件导入语句
     *   5. 构建props字符串：正确处理不同数据类型的props传递
     *   6. 生成图层渲染代码：根据图层类型决定是否使用Sequence包装
     *   7. 组装最终TSX：将所有部分组合成完整的React组件代码
     */
    _generateTSXContent(templateConfig, singlePlayDurationInFrames, totalDurationInFrames) {
        const { composition, layers, audio } = templateConfig; // 解构提取配置参数
        const mainComponentName = composition.id; // 获取主组件名称
        logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 开始生成TSX内容，组件名: ${mainComponentName}`); // 日志：记录TSX生成开始

        // 初始化导入语句集合，使用Set避免重复导入
        const imports = new Set([
            "import React from 'react';", // React基础导入
            "import { AbsoluteFill, Audio, Sequence, staticFile } from 'remotion';", // Remotion核心组件导入
            "import '../public/fonts.css';" // 导入思源黑体字体样式
        ]);
        const layerRenders = []; // 图层渲染代码数组

        // 遍历图层配置，为每个图层生成对应的React组件代码
        layers.forEach(layer => {
            // 验证图层配置完整性
            if (!layer.component) {
                logger.warn(`${this.instanceLogPrefix}[_generateTSXContent] Layer type '${layer.type}' is missing 'component'. Skipping.`); // 日志：记录缺少组件配置的警告
                return; // 跳过无效图层
            }

            const componentToRender = layer.component; // 获取要渲染的组件名
            // 规范化路径以避免Windows反斜杠问题，构建components目录下的组件路径
            // 特殊处理：progress类型不需要加s后缀，TextArea组件直接在components目录下
            let componentPath;
            if (componentToRender === 'TextArea') {
                 componentPath = `../components/${componentToRender}`.replace(/\\/g, '/');
             } else {
                 const folderName = layer.type === 'progress' ? 'progress' : `${layer.type}s`;
                 componentPath = `../components/${folderName}/${componentToRender}`.replace(/\\/g, '/');
             }
            
            // 核心修正: 根据组件类型处理不同的导入方式
            if (componentToRender === 'Background' || componentToRender === 'LinearProgress' || componentToRender === 'TextArea') {
                // Background.jsx, LinearProgress.jsx, TextArea.jsx 使用 export default
                imports.add(`import ${componentToRender} from '${componentPath}';`);
            } else {
                // 其他组件（如CenteredVideo.tsx）使用 export
                imports.add(`import { ${componentToRender} } from '${componentPath}';`);
            }
            logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 添加组件导入: ${componentToRender} from ${componentPath}`); // 日志：记录组件导入添加

            // 核心修正: 为特殊组件处理props
            const props = layer.props || {}; // 获取图层props，默认为空对象
            let propsString = '';

            if (layer.component === 'Background') {
                // 对于Background组件，将整个props对象作为单个config prop传递
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] Background组件props: ${JSON.stringify(props)}`);
                propsString = `config={${JSON.stringify(props)}}`;
            } else if (layer.component === 'LinearProgress') {
                // 对于LinearProgress组件，处理动态进度条的特殊逻辑
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] LinearProgress组件props: ${JSON.stringify(props)}`);
                
                // 替换占位符为实际的单周期时长计算
                const modifiedProps = { ...props };
                if (modifiedProps.singleCycleDuration === 'PLACEHOLDER_SINGLE_CYCLE_DURATION') {
                    modifiedProps.singleCycleDuration = singlePlayDurationInFrames; // 使用单次播放时长作为单周期时长
                    logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 替换进度条单周期时长为: ${singlePlayDurationInFrames}帧`);
                }
                
                propsString = Object.entries(modifiedProps)
                    .map(([key, value]) => {
                        if (value === undefined) return ''; // 跳过undefined值
                        if (typeof value === 'string') {
                            return `${key}="${value}"`; // 字符串类型使用双引号
                        }
                        // 对于布尔值、数字、对象和数组，使用JSON.stringify并包装在大括号中
                        return `${key}={${JSON.stringify(value)}}`;
                    })
                    .filter(p => p) // 移除空属性
                    .join(' '); // 用空格连接所有props
            } else if (componentToRender === 'CenteredVideo') {
                // 特殊处理：CenteredVideo组件强制添加volume=0确保静音
                const modifiedProps = { ...props, volume: 0 };
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 为视频组件添加volume=0确保静音`);
                
                propsString = Object.entries(modifiedProps)
                    .map(([key, value]) => {
                        if (value === undefined) return ''; // 跳过undefined值
                        if (typeof value === 'string') {
                            return `${key}="${value}"`; // 字符串类型使用双引号
                        }
                        // 对于布尔值、数字、对象和数组，使用JSON.stringify并包装在大括号中
                        return `${key}={${JSON.stringify(value)}}`;
                    })
                    .filter(p => p) // 移除空属性
                    .join(' '); // 用空格连接所有props
            } else if (componentToRender === 'TextArea') {
                // 特殊处理：TextArea组件的props处理
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] TextArea组件props: ${JSON.stringify(props)}`);
                
                propsString = Object.entries(props)
                    .map(([key, value]) => {
                        if (value === undefined) return ''; // 跳过undefined值
                        if (typeof value === 'string') {
                            return `${key}="${value}"`; // 字符串类型使用双引号
                        }
                        // 对于布尔值、数字、对象和数组，使用JSON.stringify并包装在大括号中
                        return `${key}={${JSON.stringify(value)}}`;
                    })
                    .filter(p => p) // 移除空属性
                    .join(' '); // 用空格连接所有props
            } else {
                // 对于其他组件，保持原有的props处理方式
                propsString = Object.entries(props)
                    .map(([key, value]) => {
                        if (value === undefined) return ''; // 跳过undefined值
                        if (typeof value === 'string') {
                            return `${key}="${value}"`; // 字符串类型使用双引号
                        }
                        // 对于布尔值、数字、对象和数组，使用JSON.stringify并包装在大括号中
                        return `${key}={${JSON.stringify(value)}}`;
                    })
                    .filter(p => p) // 移除空属性
                    .join(' '); // 用空格连接所有props
            }

            const componentElement = `<${componentToRender} ${propsString} />`; // 构建完整的组件元素字符串
            logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 生成组件元素: ${componentElement}`); // 日志：记录组件元素生成

            // 根据图层类型决定渲染方式
            if (layer.type === 'video') {
                // 视频图层使用Sequence包装，限制播放时长为单次播放时长
                layerRenders.push(
                    `            <Sequence from={0} durationInFrames={${singlePlayDurationInFrames}}>
                ${componentElement}
            </Sequence>`
                );
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 视频图层使用Sequence包装，时长: ${singlePlayDurationInFrames}帧`); // 日志：记录视频图层Sequence配置
            } else if (layer.type === 'videoGuide') {
                // videoGuide图层从开始到结束固定显示，覆盖整个视频时长
                layerRenders.push(
                    `            <Sequence from={0} durationInFrames={${totalDurationInFrames}}>
                ${componentElement}
            </Sequence>`
                );
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] videoGuide图层从开始到结束显示，时长: ${totalDurationInFrames}帧`); // 日志：记录videoGuide图层Sequence配置
            } else if (layer.type === 'textArea') {
                // textArea图层在视频播放完毕后显示，填充剩余音频时间
                const textAreaFrom = singlePlayDurationInFrames; // 从视频播放完毕开始
                const textAreaDuration = totalDurationInFrames - singlePlayDurationInFrames; // 剩余音频时间
                
                // 检查是否有占位符需要替换
                let finalComponentElement = componentElement;
                if (componentElement.includes('{{FROM_PLACEHOLDER}}')) {
                    finalComponentElement = componentElement.replace('{{FROM_PLACEHOLDER}}', textAreaFrom.toString());
                }
                if (finalComponentElement.includes('{{DURATION_PLACEHOLDER}}')) {
                    finalComponentElement = finalComponentElement.replace('{{DURATION_PLACEHOLDER}}', textAreaDuration.toString());
                }
                
                layerRenders.push(
                    `            <Sequence from={${textAreaFrom}} durationInFrames={${textAreaDuration}}>
                ${finalComponentElement}
            </Sequence>`
                );
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] textArea图层在视频播放完毕后显示，从第${textAreaFrom}帧开始，持续${textAreaDuration}帧`); // 日志：记录textArea图层Sequence配置
            } else {
                // 其他图层（如背景）直接渲染，不限制时长
                layerRenders.push(`            ${componentElement}`);
                logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 非视频图层直接渲染: ${layer.type}`); // 日志：记录非视频图层渲染
            }
        });

        // 序列化音频配置为JSON字符串，用于嵌入TSX代码
        const audioConfigString = JSON.stringify(audio, null, 4);
        logger.debug(`${this.instanceLogPrefix}[_generateTSXContent] 音频配置序列化完成`); // 日志：记录音频配置序列化

        // 组装最终的TSX代码字符串
        const tsxContent = `/**
 * @功能概述: 动态生成的视频组件 - ${mainComponentName}
 * @组件类型: Remotion视频组件（自动生成）
 * @生成时间: ${new Date().toISOString()}
 * @图层数量: ${layers.length}个图层
 */

${[...imports].join('\n')}

export const ${mainComponentName}: React.FC = () => {
    // 音频配置对象，从模板配置中提取
    const audioConfig = ${audioConfigString};

    return (
        <AbsoluteFill>
${layerRenders.join('\n')}

            {/* 音频组件：根据配置条件渲染 */}
            {audioConfig.enabled && (
                <Audio
                    src={staticFile(audioConfig.source)}
                    volume={audioConfig.volume}
                />
            )}
        </AbsoluteFill>
    );
};`;

        logger.info(`${this.instanceLogPrefix}[_generateTSXContent] TSX内容生成完成，包含${layers.length}个图层`); // 日志：记录TSX生成完成
        return tsxContent; // 返回生成的TSX代码
    }

    /**
     * @功能概述: 更新Root.tsx文件，注册新生成的Composition组件
     * @param {Object} templateConfig - 模板配置对象，包含composition配置
     * @param {string} fileName - 生成的TSX文件名
     * @param {number} durationInFrames - 视频总帧数
     * @returns {Promise<void>}
     * @私有方法: Root.tsx更新逻辑
     * @执行流程:
     *   1. 构建Root.tsx文件路径
     *   2. 提取组件配置参数
     *   3. 生成新的Root.tsx内容
     *   4. 写入Root.tsx文件
     */
    async _updateRootTSX(templateConfig, fileName, durationInFrames) {
        const rootPath = path.resolve(__dirname, '../remotion/Root.tsx'); // 构建Root.tsx文件路径
        const { composition } = templateConfig; // 提取composition配置
        const componentName = composition.id; // 获取组件名称
        const importName = fileName.replace('.tsx', ''); // 移除文件扩展名，用于import语句
        logger.debug(`${this.instanceLogPrefix}[_updateRootTSX] 开始更新Root.tsx，组件: ${componentName}`); // 日志：记录Root.tsx更新开始

        // 生成新的Root.tsx内容，包含Composition注册
        const newRootContent = `
import React from 'react';
import { Composition } from 'remotion';
import { ${componentName} } from './generated/${importName}';

export const Root: React.FC = () => {
    return (
        <Composition
            id="${componentName}"
            component={${componentName}}
            durationInFrames={${durationInFrames}}
            fps={${composition.fps}}
            width={${composition.width}}
            height={${composition.height}}
        />
    );
};`;

        await fs.writeFile(rootPath, newRootContent, 'utf8'); // 写入Root.tsx文件
        logger.info(`${this.instanceLogPrefix}[_updateRootTSX] Root.tsx更新完成，注册组件: ${componentName}`); // 日志：记录Root.tsx更新完成
    }

    /**
     * @功能概述: 配置转换核心方法，实现渐进式配置迁移（新旧配置体系的平滑过渡）
     * @param {Object} context - 任务上下文，包含videoConfig和可选的remotionTemplate
     * @param {string} copiedAudioPath - 复制到public目录的音频文件路径
     * @param {string} copiedVideoPath - 复制到public目录的视频文件路径
     * @returns {Promise<Object>} 最终的模板配置对象
     * @私有方法: 配置转换核心逻辑
     * @架构关键: 实现渐进式迁移模式，优先使用remotionTemplate，回退到videoConfig
     * @执行流程:
     *   1. 提取配置参数：从context中解构videoConfig和remotionTemplate
     *   2. 转换音频配置：统一处理音频相关配置
     *   3. 配置体系判断：检查是否存在新配置体系remotionTemplate
     *   4. 新配置处理：遍历layers数组，加载微模板JSON文件
     *   5. 旧配置回退：如果没有新配置，使用videoConfig进行兼容处理
     *   6. 返回统一格式：无论使用哪种配置，都返回标准的模板配置对象
     */
    async _convertVideoConfigToTemplate(context, copiedAudioPath, copiedVideoPath) {
        const { videoConfig, remotionTemplate } = context; // 解构提取配置参数
        const audioConfig = this._convertAudioConfig(videoConfig, copiedAudioPath); // 转换音频配置
        logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 开始配置转换，检查配置体系类型`); // 日志：记录配置转换开始

        // 优先使用新配置体系 (remotionTemplate) - 渐进式迁移的核心逻辑
        if (remotionTemplate && remotionTemplate.layers) {
            logger.info(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 使用新版 remotionTemplate 配置。`); // 日志：记录使用新配置体系

            // 遍历layers数组，为每个图层加载对应的微模板JSON文件
            const layers = await Promise.all(remotionTemplate.layers.map(async (layer) => {
                // 构建微模板文件路径：templates/{type}s/{template}.json
                const templatePath = path.join(this.templatesDir, `${layer.type}s`, `${layer.template}.json`);
                logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 加载微模板: ${templatePath}`); // 日志：记录微模板加载

                const content = await fs.readFile(templatePath, 'utf8'); // 读取微模板文件
                const templateJson = JSON.parse(content); // 解析JSON配置
                
                // JSON Schema验证
                const isValid = this.validateMicroTemplate(templateJson);
                if (!isValid) {
                    const errors = this.validateMicroTemplate.errors.map(err => 
                        `${err.instancePath}: ${err.message}`
                    ).join(', ');
                    logger.warn(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 微模板格式验证失败: ${templatePath}, 错误: ${errors}`);
                    // 继续执行但记录警告，保持向后兼容
                }

                // 核心修正: 支持新的微模板标准格式 {metadata, component, config}
                // 优先级：layer.props > templateJson.config（指令层覆盖模板层）
                let templateProps = {};
                
                // 检查是否为新标准格式
                if (templateJson.config) {
                    // 新标准格式：使用config字段作为组件配置
                    templateProps = { ...templateJson.config };
                    logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 使用新标准格式微模板: ${templateJson.metadata?.name || 'unknown'}`);
                } else {
                    // 兼容旧格式：从根级别提取配置（排除元数据字段）
                    templateProps = { ...templateJson };
                    delete templateProps.name;
                    delete templateProps.component;
                    delete templateProps.description;
                    delete templateProps.source_description;
                    if (templateProps.overlay && templateProps.overlay.enabled_description) {
                        delete templateProps.overlay.enabled_description;
                        delete templateProps.overlay.color_description;
                        delete templateProps.overlay.opacity_description;
                    }
                    logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 使用兼容旧格式微模板`);
                }
                
                let finalProps = { ...templateProps, ...(layer.props || {}) };
                if (layer.type === 'video') {
                    finalProps.src = copiedVideoPath; // 为视频图层注入复制后的视频文件路径
                    logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 为视频图层注入src: ${copiedVideoPath}`); // 日志：记录视频路径注入
                }
                
                logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 图层${layer.type}最终props: ${JSON.stringify(finalProps)}`); // 调试日志

                // 返回标准化的图层配置对象
                return {
                    type: layer.type, // 图层类型（background、video等）
                    template: layer.template, // 使用的微模板名称
                    component: templateJson.component, // 组件名来自微模板
                    props: finalProps // 合并后的最终props
                };
            }));

            logger.info(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 新配置体系处理完成，生成${layers.length}个图层`); // 日志：记录新配置处理完成

            // 返回新配置体系的标准模板格式
            return {
                composition: {
                    id: remotionTemplate.composition?.id || "GeneratedVideo", // 组件ID，默认为GeneratedVideo
                    width: videoConfig.width, // 视频宽度（仍从videoConfig获取）
                    height: videoConfig.height, // 视频高度（仍从videoConfig获取）
                    fps: videoConfig.framerate, // 帧率（仍从videoConfig获取）
                },
                layers, // 处理后的图层数组
                audio: audioConfig // 音频配置
            };
        }

        // 回退到旧配置体系 (videoConfig) - 向后兼容处理
        logger.warn(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 正在使用旧版 videoConfig 进行回退兼容。`); // 日志：记录使用旧配置体系

        // 转换背景样式配置为组件props
        const backgroundProps = await this._convertBackgroundStyleToProps(videoConfig.backgroundStyle, videoConfig.backgroundColor);
        logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 背景配置转换完成，backgroundProps: ${JSON.stringify(backgroundProps)}`); // 日志：记录背景配置转换

        // 构建标准化的图层数组（旧配置体系的固定结构）
        const layers = [
            {
                type: 'background', // 背景图层
                component: 'Background', // 使用Background组件
                props: backgroundProps // 背景组件的props
            },
            {
                type: 'video', // 视频图层
                component: 'CenteredVideo', // 使用CenteredVideo组件
                props: {
                    src: copiedVideoPath, // 核心修正: 属性名从 `source` 改为 `src`，匹配组件期望
                    isMuted: true // 强制静音：插入的视频不允许有声音
                }
            }
        ];

        // 检查是否需要添加进度条图层（旧配置体系兼容）
        if (videoConfig.progressBar) {
            logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 检测到progressBar配置，添加进度条图层`); // 日志：记录进度条检测
            
            // 转换旧配置格式为新的进度条图层配置
            const progressBarLayer = {
                type: 'progress', // 进度条图层类型
                component: 'LinearProgress', // 使用LinearProgress组件
                props: {
                    config: {
                        position: {
                            height: videoConfig.progressBar.height + 'px',
                            top: '65.8%', // 固定位置：紧贴16:9视频下方（计算：视频底部1263.75px / 画布高度1920px = 65.8%）
                            width: '100%', // 与视频宽度保持一致
                            left: '0%' // 居中对齐
                        },
                        style: {
                            backgroundColor: videoConfig.progressBar.backgroundColor,
                            progressColor: videoConfig.progressBar.foregroundColor,
                            borderRadius: '0px'
                        },
                        animation: {
                            type: 'cyclic',
                            singleCycleDuration: 'auto',
                            resetBehavior: 'immediate'
                        }
                    },
                    singleCycleDuration: 'PLACEHOLDER_SINGLE_CYCLE_DURATION' // 占位符，将在TSX生成时替换
                }
            };
            
            layers.push(progressBarLayer);
            logger.info(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 进度条图层已添加到layers数组`); // 日志：记录进度条添加
        }

        // 检查是否需要添加videoGuide图层（向前兼容处理）
        if (videoConfig.subtitleConfig && videoConfig.subtitleConfig.videoGuide && videoConfig.subtitleConfig.videoGuide.enabled) {
            logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 检测到videoGuide配置，添加视频引导语图层`); // 日志：记录videoGuide检测
            
            const videoGuideConfig = videoConfig.subtitleConfig.videoGuide;
            const baseStyle = this._convertCSSToReactStyle(videoGuideConfig.style);
            
            // 创建title1图层
            if (videoGuideConfig.title1) {
                // 为title1创建专用样式（使用top1位置）
                const title1Style = { ...baseStyle };
                delete title1Style.top1;
                delete title1Style.top2;
                
                const title1Layer = {
                    type: 'videoGuide', // 使用videoGuide类型，确保从开始到结束都显示
                    component: 'TextArea', // 使用TextArea组件
                    props: {
                        text: videoGuideConfig.title1,
                        backgroundColor: 'transparent', // 透明背景
                        width: '1080px',
                        height: 'auto',
                        style: {
                            backgroundColor: 'transparent',
                            width: '1080px',
                            height: 'auto',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            position: 'absolute',
                            left: '540px',
                            top: '330px', // 用户要求的新位置
                            transform: 'translateX(-50%)',
                            fontSize: '100px',
                            fontFamily: 'Source Han Serif SC VF, Source Han Serif, SimSun, serif',
                            fontWeight: 'bold',
                            color: 'rgb(255, 255, 255)',
                            textShadow: 'rgba(0, 0, 0, 0.8) 2px 2px 4px',
                            textAlign: 'center',
                            whiteSpace: 'nowrap',
                            zIndex: 1000
                        },
                        innerStyle: {
                            color: 'rgb(255, 255, 255)',
                            fontSize: '70px',
                            fontFamily: 'sans-serif',
                            textAlign: 'center',
                            padding: '20px',
                            overflowWrap: 'break-word',
                            maxWidth: '100%',
                            maxHeight: '100%',
                            overflow: 'hidden',
                            fontWeight: '900'
                        }
                    }
                };
                layers.unshift(title1Layer); // 插入到数组开头，确保在最前面渲染
                logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] title1图层已添加: ${videoGuideConfig.title1}`);
            }
            
            // 创建title2图层
            if (videoGuideConfig.title2) {
                // 为title2创建专用样式（使用top2位置）
                const title2Style = { ...baseStyle };
                delete title2Style.top1;
                delete title2Style.top2;
                
                const title2Layer = {
                    type: 'videoGuide', // 使用videoGuide类型，确保从开始到结束都显示
                    component: 'TextArea', // 使用TextArea组件
                    props: {
                        text: videoGuideConfig.title2,
                        backgroundColor: 'transparent', // 透明背景
                        width: '1080px',
                        height: 'auto',
                        style: {
                            backgroundColor: 'transparent',
                            width: '1080px',
                            height: 'auto',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            position: 'absolute',
                            left: '540px',
                            top: '460px', // 用户要求的新位置
                            transform: 'translateX(-50%)',
                            fontSize: '100px',
                            fontFamily: 'Source Han Serif SC VF, Source Han Serif, SimSun, serif',
                            fontWeight: 'bold',
                            color: 'rgb(255, 255, 255)',
                            textShadow: 'rgba(0, 0, 0, 0.8) 2px 2px 4px',
                            textAlign: 'center',
                            whiteSpace: 'nowrap',
                            zIndex: 1000
                        },
                        innerStyle: {
                            color: 'rgb(255, 255, 255)',
                            fontSize: '70px',
                            fontFamily: 'sans-serif',
                            textAlign: 'center',
                            padding: '5px 20px 20px',
                            overflowWrap: 'break-word',
                            maxWidth: '100%',
                            maxHeight: '100%',
                            overflow: 'hidden',
                            fontWeight: '900'
                        }
                    }
                };
                layers.unshift(title2Layer); // 插入到数组开头，确保在最前面渲染
                logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] title2图层已添加: ${videoGuideConfig.title2}`);
            }
            
            const addedLayersCount = (videoGuideConfig.title1 ? 1 : 0) + (videoGuideConfig.title2 ? 1 : 0);
            logger.info(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] videoGuide图层处理完成，已添加${addedLayersCount}个图层`);
        }

        // 检查是否需要添加文字区域图层（旧配置体系兼容）
        if (videoConfig.textArea || videoConfig.textAreaBackgroundColor) {
            logger.debug(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 检测到textArea配置，添加文字区域图层`); // 日志：记录文字区域检测
            
            // 获取文字区域配置，支持两种配置方式的向前兼容
            const textAreaConfig = videoConfig.textArea || {};
            const backgroundColor = textAreaConfig.backgroundColor || videoConfig.textAreaBackgroundColor || '#3B3B3B';
            const width = textAreaConfig.width || '1080px';
            const height = textAreaConfig.height || '608px';
            
            // 转换旧配置格式为新的文字区域图层配置
            const textAreaLayer = {
                type: 'textArea', // 文字区域图层类型
                component: 'TextArea', // 使用TextArea组件
                props: {
                    backgroundColor: backgroundColor,
                    width: width,
                    height: height,
                    position: {
                        top: '656.25px', // 16:9视频在1080x1920画布中的顶部位置
                        left: '0px'
                    }
                },
                timing: {
                    showAfterVideo: true, // 标记：在视频播放完毕后显示
                    from: 'PLACEHOLDER_VIDEO_END_FRAME', // 占位符，将在TSX生成时替换为视频结束帧
                    durationInFrames: 'PLACEHOLDER_REMAINING_FRAMES' // 占位符，将在TSX生成时替换为剩余帧数
                }
            };
            
            layers.push(textAreaLayer);
            logger.info(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 文字区域图层已添加到layers数组，背景色: ${backgroundColor}，尺寸: ${width}x${height}`); // 日志：记录文字区域添加
        }

        logger.info(`${this.instanceLogPrefix}[_convertVideoConfigToTemplate] 旧配置体系处理完成，生成${layers.length}个图层`); // 日志：记录旧配置处理完成

        // 返回旧配置体系的标准模板格式
        return {
            composition: {
                id: "GeneratedVideo", // 固定的组件ID
                width: videoConfig.width, // 视频宽度
                height: videoConfig.height, // 视频高度
                fps: videoConfig.framerate, // 帧率
            },
            layers, // 构建的图层数组
            audio: audioConfig // 音频配置
        };
    }

    /**
     * @功能概述: 将背景样式配置转换为Background组件的props
     * @param {string} backgroundStyle - 背景样式名称（如"newspaper"、"abstract"）
     * @param {string} fallbackColor - 回退颜色，当背景模板加载失败时使用
     * @returns {Promise<Object>} Background组件的props对象
     * @私有方法: 背景配置转换逻辑
     * @执行流程:
     *   1. 构建回退props：使用符合Background组件期望的纯色背景配置
     *   2. 检查背景样式：如果为空或为"color"，直接返回回退props
     *   3. 加载背景模板：从templates/backgrounds/目录加载对应的JSON文件
     *   4. 提取配置：从模板JSON中提取Background组件需要的配置字段
     *   5. 错误处理：如果加载失败，记录警告并返回回退props
     * @兼容性: 支持旧配置文件（video-config.json）中的backgroundStyle字段
     */
    async _convertBackgroundStyleToProps(backgroundStyle, fallbackColor) {
        // 核心修正: 构建符合Background组件期望的回退props格式
        // Background组件期望config对象包含type、fallback等属性，而不是style属性
        const fallbackProps = {
            type: 'color', // 指定背景类型为纯色
            fallback: {
                color: fallbackColor || '#000000' // 默认黑色背景，符合用户要求
            }
        };

        // 检查背景样式，如果为空或为"color"，直接使用纯色背景
        if (!backgroundStyle || backgroundStyle === 'color') {
            logger.debug(`${this.instanceLogPrefix}[_convertBackgroundStyleToProps] 使用纯色背景，颜色: ${fallbackProps.fallback.color}`); // 日志：记录使用纯色背景
            return fallbackProps;
        }

        try {
            // 构建背景模板文件路径
            const templatePath = path.join(this.templatesDir, 'backgrounds', `${backgroundStyle}.json`);
            const templateContent = await fs.readFile(templatePath, 'utf8'); // 读取模板文件
            const templateJson = JSON.parse(templateContent); // 解析JSON配置
            logger.debug(`${this.instanceLogPrefix}[_convertBackgroundStyleToProps] 成功加载背景模板: ${backgroundStyle}`); // 日志：记录模板加载成功

            // 核心修正: 从模板的config字段中提取Background组件需要的配置
            // 背景模板的实际配置在config字段中，而不是根级别
            const templateConfig = templateJson.config || templateJson;
            const backgroundConfig = {
                type: templateConfig.type || 'image', // 背景类型
                source: templateConfig.source, // 背景资源路径
                overlay: templateConfig.overlay, // 遮罩配置
                fallback: templateConfig.fallback || fallbackProps.fallback // 回退配置
            };
            
            logger.info(`${this.instanceLogPrefix}[_convertBackgroundStyleToProps] 背景配置转换完成，类型: ${backgroundConfig.type}, 资源: ${backgroundConfig.source || '无'}`); // 日志：记录配置转换完成
            return backgroundConfig;
        } catch (error) {
            // 错误处理：记录警告并返回回退props
            logger.warn(`${this.instanceLogPrefix}[_convertBackgroundStyleToProps] 读取背景模板 '${backgroundStyle}.json' 失败，将回退到黑色背景。错误: ${error.message}`); // 日志：记录模板加载失败
            return fallbackProps;
        }
    }

    /**
     * @功能概述: 将CSS样式配置转换为React样式对象（videoGuide向前兼容）
     * @param {Object} cssStyle - CSS样式配置对象
     * @returns {Object} React样式对象
     * @私有方法: CSS到React样式转换逻辑
     * @执行流程:
     *   1. 处理字体相关属性：fontSize、fontFamily、fontWeight、color
     *   2. 处理文字阴影：textShadow
     *   3. 处理位置信息：position.x、position.y1、position.y2
     *   4. 错误处理：样式转换失败时使用默认样式并记录警告
     */
    _convertCSSToReactStyle(cssStyle) {
        try {
            if (!cssStyle) {
                logger.warn(`${this.instanceLogPrefix}[_convertCSSToReactStyle] CSS样式配置为空，使用默认样式`);
                return this._getDefaultVideoGuideStyle();
            }

            const reactStyle = {};

            // 处理字体大小
            if (cssStyle.fontSize) {
                reactStyle.fontSize = cssStyle.fontSize;
            }

            // 处理字体族
            if (cssStyle.fontFamily) {
                reactStyle.fontFamily = cssStyle.fontFamily;
            }

            // 处理字体粗细
            if (cssStyle.fontWeight) {
                reactStyle.fontWeight = cssStyle.fontWeight;
            }

            // 处理字体颜色
            if (cssStyle.color) {
                reactStyle.color = cssStyle.color;
            }

            // 处理文字阴影
            if (cssStyle.textShadow) {
                reactStyle.textShadow = cssStyle.textShadow;
            }

            // 处理位置信息
            if (cssStyle.position) {
                if (cssStyle.position.x) {
                    // 将x坐标转换为left属性
                    reactStyle.left = cssStyle.position.x;
                }
                if (cssStyle.position.y1) {
                    // 第一行的y坐标
                    reactStyle.top1 = cssStyle.position.y1;
                }
                if (cssStyle.position.y2) {
                    // 第二行的y坐标
                    reactStyle.top2 = cssStyle.position.y2;
                }
            }

            // 添加文本对齐
            reactStyle.textAlign = 'center';
            reactStyle.whiteSpace = 'nowrap';

            logger.debug(`${this.instanceLogPrefix}[_convertCSSToReactStyle] CSS样式转换完成: ${JSON.stringify(reactStyle)}`);
            return reactStyle;

        } catch (error) {
            logger.warn(`${this.instanceLogPrefix}[_convertCSSToReactStyle] CSS样式转换失败: ${error.message}，使用默认样式`);
            return this._getDefaultVideoGuideStyle();
        }
    }

    /**
     * @功能概述: 获取videoGuide的默认样式
     * @returns {Object} 默认React样式对象
     * @私有方法: 默认样式配置
     */
    _getDefaultVideoGuideStyle() {
        return {
            fontSize: '100px',
            fontFamily: 'Arial',
            fontWeight: 'bold',
            color: '#FFFFFF',
            textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
            textAlign: 'center',
            whiteSpace: 'nowrap',
            left: '50%',
            top1: '300px',
            top2: '420px'
        };
    }

    /**
     * @功能概述: 将videoConfig中的音频配置转换为标准的音频配置对象
     * @param {Object} videoConfig - 视频配置对象
     * @param {string} copiedAudioPath - 复制到public目录的音频文件路径
     * @returns {Object} 标准化的音频配置对象
     * @私有方法: 音频配置转换逻辑
     */
    _convertAudioConfig(videoConfig, copiedAudioPath) {
        const audioConfig = {
            enabled: videoConfig.audio?.enabled ?? true, // 默认启用音频
            source: copiedAudioPath, // 使用复制后的音频文件路径
            volume: videoConfig.audio?.volume ?? 1, // 默认音量为1
            loop: false, // 不循环，因为已经通过重复拼接处理了
        };
        logger.debug(`${this.instanceLogPrefix}[_convertAudioConfig] 音频配置转换完成: ${JSON.stringify(audioConfig)}`); // 日志：记录音频配置转换
        return audioConfig;
    }

    /**
     * @功能概述: 复制音频文件到public目录，支持音频重复拼接功能
     * @param {string} audioFilePath - 原始音频文件路径
     * @param {number} repeatCount - 重复次数，支持教育视频的"听三遍"模式
     * @returns {Promise<string>} 复制后的音频文件相对路径（相对于public目录）
     * @私有方法: 音频文件处理逻辑
     * @执行流程:
     *   1. 创建audio目录：确保public/audio/目录存在
     *   2. 生成唯一文件名：使用时间戳和随机字符串避免冲突
     *   3. 判断处理方式：根据repeatCount决定是直接复制还是重复拼接
     *   4. 执行文件操作：调用FFmpeg或直接复制文件
     *   5. 返回相对路径：返回相对于public目录的路径供staticFile使用
     */
    async _copyAudioToPublic(audioFilePath, repeatCount) {
        const audioDir = path.join(this.publicDir, 'audio'); // 构建audio目录路径
        await fs.mkdir(audioDir, { recursive: true }); // 确保目录存在

        // 生成唯一的音频文件名，避免文件名冲突
        const audioFileName = `audio_${Date.now()}_${Math.random().toString(36).substring(2, 8)}.mp3`;
        const outputPath = path.join(audioDir, audioFileName); // 构建完整输出路径

        if (repeatCount > 1) {
            // 需要重复拼接，使用FFmpeg处理
            logger.info(`${this.instanceLogPrefix}[_copyAudioToPublic] 开始音频重复拼接，重复${repeatCount}次`); // 日志：记录重复拼接开始
            await this._repeatAudioWithFFmpeg(audioFilePath, outputPath, repeatCount);
            logger.info(`${this.instanceLogPrefix}[_copyAudioToPublic] 音频重复拼接完成: ${audioFileName}`); // 日志：记录重复拼接完成
        } else {
            // 不需要重复，直接复制文件
            await fs.copyFile(audioFilePath, outputPath);
            logger.debug(`${this.instanceLogPrefix}[_copyAudioToPublic] 音频文件直接复制完成: ${audioFileName}`); // 日志：记录直接复制完成
        }

        return `audio/${audioFileName}`; // 返回相对路径供staticFile使用
    }

    /**
     * @功能概述: 复制视频文件到public目录
     * @param {string} videoFilePath - 原始视频文件路径
     * @returns {Promise<string>} 复制后的视频文件相对路径（相对于public目录）
     * @私有方法: 视频文件处理逻辑
     * @执行流程:
     *   1. 创建videos目录：确保public/videos/目录存在
     *   2. 生成唯一文件名：使用时间戳和随机字符串避免冲突
     *   3. 复制视频文件：直接复制到public目录
     *   4. 返回相对路径：返回相对于public目录的路径供staticFile使用
     */
    async _copyVideoToPublic(videoFilePath) {
        const videoDir = path.join(this.publicDir, 'videos'); // 构建videos目录路径
        await fs.mkdir(videoDir, { recursive: true }); // 确保目录存在

        // 生成唯一的视频文件名，避免文件名冲突
        const videoFileName = `video_${Date.now()}_${Math.random().toString(36).substring(2, 8)}.mp4`;
        const outputPath = path.join(videoDir, videoFileName); // 构建完整输出路径

        await fs.copyFile(videoFilePath, outputPath); // 复制视频文件
        logger.debug(`${this.instanceLogPrefix}[_copyVideoToPublic] 视频文件复制完成: ${videoFileName}`); // 日志：记录视频复制完成

        return `videos/${videoFileName}`; // 返回相对路径供staticFile使用
    }

    /**
     * @功能概述: 使用FFmpeg进行音频重复拼接，实现无缝音频循环
     * @param {string} inputPath - 输入音频文件路径
     * @param {string} outputPath - 输出音频文件路径
     * @param {number} repeatCount - 重复次数
     * @returns {Promise<void>}
     * @私有方法: FFmpeg音频处理核心逻辑
     * @执行流程:
     *   1. 构建FFmpeg参数：为每次重复添加输入参数
     *   2. 构建concat滤镜：使用FFmpeg的concat滤镜进行无缝拼接
     *   3. 启动FFmpeg进程：使用spawn启动FFmpeg子进程
     *   4. 监听进程事件：处理成功、失败和错误事件
     *   5. 返回Promise：异步处理完成后resolve或reject
     */
    async _repeatAudioWithFFmpeg(inputPath, outputPath, repeatCount) {
        return new Promise((resolve, reject) => {
            // 构建FFmpeg输入参数：为每次重复添加 -i inputPath
            const inputArgs = Array(repeatCount).fill(['-i', inputPath]).flat();

            // 构建concat滤镜的输入标识符：[0:a][1:a][2:a]...
            const filterInputs = Array.from({ length: repeatCount }, (_, i) => `[${i}:a]`).join('');

            // 构建concat滤镜：拼接所有音频流
            const concatFilter = `${filterInputs}concat=n=${repeatCount}:v=0:a=1[outa]`;

            // 构建完整的FFmpeg参数数组
            const ffmpegArgs = [
                ...inputArgs,                    // 多个输入文件
                '-filter_complex', concatFilter, // 音频拼接滤镜
                '-map', '[outa]',               // 映射输出音频流
                '-c:a', 'libmp3lame',           // MP3编码器
                '-b:a', '192k',                 // 音频比特率
                '-y',                           // 覆盖输出文件
                outputPath                      // 输出路径
            ];

            logger.debug(`${this.instanceLogPrefix}[_repeatAudioWithFFmpeg] FFmpeg命令: ffmpeg ${ffmpegArgs.join(' ')}`); // 日志：记录FFmpeg命令

            // 启动FFmpeg进程
            const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);
            let ffmpegError = ''; // 收集错误输出

            // 监听stderr输出（FFmpeg的日志和错误信息）
            ffmpegProcess.stderr.on('data', (data) => {
                ffmpegError += data.toString();
            });

            // 监听进程退出事件
            ffmpegProcess.on('close', (code) => {
                if (code === 0) {
                    logger.debug(`${this.instanceLogPrefix}[_repeatAudioWithFFmpeg] FFmpeg音频拼接成功`); // 日志：记录拼接成功
                    resolve(); // 成功完成
                } else {
                    const errorMsg = `FFmpeg音频拼接失败，退出码: ${code}. FFmpeg输出: ${ffmpegError.slice(-500)}`;
                    logger.error(`${this.instanceLogPrefix}[_repeatAudioWithFFmpeg] ${errorMsg}`); // 日志：记录拼接失败
                    reject(new Error(errorMsg)); // 拒绝Promise
                }
            });

            // 监听进程启动错误
            ffmpegProcess.on('error', (error) => {
                const errorMsg = `FFmpeg进程启动失败: ${error.message}`;
                logger.error(`${this.instanceLogPrefix}[_repeatAudioWithFFmpeg] ${errorMsg}`); // 日志：记录进程启动失败
                reject(new Error(errorMsg)); // 拒绝Promise
            });
        });
    }

    /**
     * @功能概述: 验证上下文中的必需字段，确保任务执行的前置条件满足
     * @param {Object} context - 任务上下文对象
     * @param {Array<string>} requiredFields - 必需字段名称数组
     * @param {string} logPrefix - 日志前缀，用于错误日志记录
     * @throws {Error} 当缺少必需字段时抛出错误
     * @私有方法: 参数验证逻辑
     */
    validateRequiredFields(context, requiredFields, logPrefix) {
        for (const field of requiredFields) {
            if (context[field] === undefined || context[field] === null) {
                const errorMsg = `执行失败：上下文缺少必需字段 "${field}"`;
                logger.error(`${logPrefix}[ERROR] ${errorMsg}`); // 日志：记录字段缺失错误
                throw new Error(errorMsg); // 抛出错误，终止任务执行
            }
        }
        logger.debug(`${logPrefix} 必需字段验证通过: [${requiredFields.join(', ')}]`); // 日志：记录验证通过
    }

    /**
     * @功能概述: 收集任务的详细上下文信息，扩展TaskBase的基础上下文
     * @returns {Object} 包含任务特定信息的详细上下文对象
     * @重写方法: 扩展TaskBase的collectDetailedContext方法
     * @用途: 用于调试、日志记录和任务状态追踪
     */
    collectDetailedContext() {
        const baseContext = super.collectDetailedContext(); // 获取TaskBase的基础上下文
        const taskResult = this.result || {}; // 获取任务执行结果

        // 扩展上下文信息，添加RemotionTSXGeneratorTask特定的信息
        return {
            ...baseContext, // 保留基础上下文信息
            inputContext: {
                ...baseContext.inputContext, // 保留基础输入上下文
                originalVideoPath: this.context?.originalVideoPath || 'N/A', // 原始视频文件路径
                originalAudioPath: this.context?.audioFilePath || 'N/A', // 原始音频文件路径
                originalAudioDuration: this.context?.audioDuration || 'N/A', // 原始音频时长
            },
            outputContext: {
                ...baseContext.outputContext, // 保留基础输出上下文
                copiedVideoPath: taskResult.copiedVideoPath || 'N/A', // 复制后的视频文件路径
                copiedAudioPath: taskResult.copiedAudioPath || 'N/A', // 复制后的音频文件路径
                totalAudioDuration: taskResult.totalAudioDuration || 'N/A', // 总音频时长（重复后）
            },
            technicalDetails: {
                ...baseContext.technicalDetails, // 保留基础技术详情
                ffmpegUsedForAudio: taskResult.repeatCount > 1, // 是否使用了FFmpeg进行音频处理
                repeatCount: taskResult.repeatCount || 'N/A', // 音频重复次数
            },
        };
    }
}

module.exports = RemotionTSXGeneratorTask;
